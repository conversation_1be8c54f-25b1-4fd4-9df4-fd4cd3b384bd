import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class StatusTimelineCard extends StatefulWidget {
  final Map<String, dynamic> missionData;

  const StatusTimelineCard({
    Key? key,
    required this.missionData,
  }) : super(key: key);

  @override
  State<StatusTimelineCard> createState() => _StatusTimelineCardState();
}

class _StatusTimelineCardState extends State<StatusTimelineCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final timeline = widget.missionData['timeline'] as List<dynamic>? ??
        _getDefaultTimeline();

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'timeline',
                      color: AppTheme.lightTheme.primaryColor,
                      size: 20,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      'Status Timeline',
                      style:
                          AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(1.w),
                    child: CustomIconWidget(
                      iconName: _isExpanded ? 'expand_less' : 'expand_more',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            _buildTimelineItems(timeline),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineItems(List<dynamic> timeline) {
    final visibleItems = _isExpanded ? timeline : timeline.take(3).toList();

    return Column(
      children: visibleItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value as Map<String, dynamic>;
        final isLast = index == visibleItems.length - 1;

        return _buildTimelineItem(item, isLast);
      }).toList(),
    );
  }

  Widget _buildTimelineItem(Map<String, dynamic> item, bool isLast) {
    final status = item['status'] as String? ?? '';
    final timestamp = item['timestamp'] as String? ?? '';
    final description = item['description'] as String? ?? '';
    final isCompleted = item['completed'] as bool? ?? false;

    Color statusColor = _getTimelineStatusColor(status, isCompleted);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: isCompleted ? statusColor : Colors.transparent,
                border: Border.all(
                  color: statusColor,
                  width: 2,
                ),
                shape: BoxShape.circle,
              ),
              child: isCompleted
                  ? Center(
                      child: CustomIconWidget(
                        iconName: 'check',
                        color: Colors.white,
                        size: 8,
                      ),
                    )
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 6.h,
                color: statusColor.withValues(alpha: 0.3),
              ),
          ],
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 2.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      status,
                      style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isCompleted
                            ? AppTheme.lightTheme.colorScheme.onSurface
                            : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (_isExpanded)
                      Text(
                        timestamp,
                        style:
                            AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color:
                              AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
                if (_isExpanded && description.isNotEmpty) ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    description,
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getTimelineStatusColor(String status, bool isCompleted) {
    if (!isCompleted) {
      return AppTheme.lightTheme.colorScheme.outline;
    }

    switch (status.toLowerCase()) {
      case 'mission created':
      case 'mission assigned':
        return AppTheme.lightTheme.primaryColor;
      case 'mission accepted':
        return AppTheme.successLight;
      case 'mission started':
      case 'in progress':
        return AppTheme.warningLight;
      case 'mission completed':
        return AppTheme.successLight;
      case 'mission rejected':
        return AppTheme.errorLight;
      default:
        return AppTheme.lightTheme.colorScheme.outline;
    }
  }

  List<Map<String, dynamic>> _getDefaultTimeline() {
    return [
      {
        'status': 'Mission Created',
        'timestamp': 'Jul 19, 2025 - 10:30 AM',
        'description': 'Mission assigned to driver by fleet manager',
        'completed': true,
      },
      {
        'status': 'Mission Assigned',
        'timestamp': 'Jul 19, 2025 - 10:35 AM',
        'description': 'Driver received mission notification',
        'completed': true,
      },
      {
        'status': 'Mission Accepted',
        'timestamp': 'Jul 19, 2025 - 11:00 AM',
        'description': 'Driver accepted the mission assignment',
        'completed': true,
      },
      {
        'status': 'Mission Started',
        'timestamp': 'Jul 19, 2025 - 2:30 PM',
        'description': 'Driver started the mission and began route',
        'completed': true,
      },
      {
        'status': 'In Progress',
        'timestamp': 'Jul 19, 2025 - 3:45 PM',
        'description': 'Mission currently in progress',
        'completed': false,
      },
      {
        'status': 'Mission Completed',
        'timestamp': 'Pending',
        'description': 'Mission completion pending',
        'completed': false,
      },
    ];
  }
}
