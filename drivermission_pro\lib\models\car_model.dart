class CarModel {
  final String id;
  final String carModel;
  final String licensePlate;
  final String status;
  final int? year;
  final String? color;
  final int mileage;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const CarModel({
    required this.id,
    required this.carModel,
    required this.licensePlate,
    required this.status,
    this.year,
    this.color,
    required this.mileage,
    this.createdAt,
    this.updatedAt,
  });

  factory CarModel.fromJson(Map<String, dynamic> json) {
    return CarModel(
      id: json['id'] as String,
      carModel: json['car_model'] as String,
      licensePlate: json['license_plate'] as String,
      status: json['status'] as String,
      year: json['year'] as int?,
      color: json['color'] as String?,
      mileage: json['mileage'] as int? ?? 0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'car_model': carModel,
      'license_plate': licensePlate,
      'status': status,
      'year': year,
      'color': color,
      'mileage': mileage,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String get displayStatus {
    switch (status) {
      case 'available':
        return 'Available';
      case 'in_use':
        return 'In Use';
      case 'maintenance':
        return 'Maintenance';
      case 'out_of_service':
        return 'Out of Service';
      default:
        return status;
    }
  }

  String get displayInfo {
    final parts = <String>[carModel];

    if (year != null) {
      parts.add('($year)');
    }
    if (color != null) {
      parts.add('- $color');
    }

    return parts.join(' ');
  }

  bool get isAvailable => status == 'available';
  bool get isInUse => status == 'in_use';
  bool get needsMaintenance => status == 'maintenance';

  CarModel copyWith({
    String? id,
    String? carModel,
    String? licensePlate,
    String? status,
    int? year,
    String? color,
    int? mileage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CarModel(
      id: id ?? this.id,
      carModel: carModel ?? this.carModel,
      licensePlate: licensePlate ?? this.licensePlate,
      status: status ?? this.status,
      year: year ?? this.year,
      color: color ?? this.color,
      mileage: mileage ?? this.mileage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
