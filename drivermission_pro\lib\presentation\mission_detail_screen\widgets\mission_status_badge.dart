import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class MissionStatusBadge extends StatelessWidget {
  final String status;
  final bool isDarkMode;

  const MissionStatusBadge({
    Key? key,
    required this.status,
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color statusColor = _getStatusColor();
    Color textColor = _getTextColor();
    String displayStatus = _getDisplayStatus();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 2.w),
          Text(
            displayStatus,
            style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
              color: textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (status.toLowerCase()) {
      case 'pending':
        return isDarkMode ? AppTheme.warningDark : AppTheme.warningLight;
      case 'accepted':
        return isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight;
      case 'in-progress':
        return isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight;
      case 'completed':
        return isDarkMode ? AppTheme.successDark : AppTheme.successLight;
      case 'rejected':
        return isDarkMode ? AppTheme.errorDark : AppTheme.errorLight;
      default:
        return isDarkMode ? AppTheme.secondaryDark : AppTheme.secondaryLight;
    }
  }

  Color _getTextColor() {
    switch (status.toLowerCase()) {
      case 'pending':
        return isDarkMode ? AppTheme.onWarningDark : AppTheme.onWarningLight;
      case 'accepted':
      case 'in-progress':
        return isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight;
      case 'completed':
        return isDarkMode ? AppTheme.onSuccessDark : AppTheme.onSuccessLight;
      case 'rejected':
        return isDarkMode ? AppTheme.onErrorDark : AppTheme.onErrorLight;
      default:
        return isDarkMode
            ? AppTheme.onSecondaryDark
            : AppTheme.onSecondaryLight;
    }
  }

  String _getDisplayStatus() {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'in-progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'rejected':
        return 'Rejected';
      default:
        return status;
    }
  }
}
