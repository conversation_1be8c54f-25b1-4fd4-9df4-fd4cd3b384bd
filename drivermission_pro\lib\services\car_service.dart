import 'package:flutter/foundation.dart';
import '../utils/supabase_service.dart';
import '../models/car_model.dart';

class CarService {
  static final CarService _instance = CarService._internal();
  factory CarService() => _instance;
  CarService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Get all cars
  Future<List<CarModel>> getAllCars() async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockCars();
      }

      final response = await client
          .from('cars')
          .select('*')
          .order('created_at', ascending: false);

      return response.map<CarModel>((json) => CarModel.fromJson(json)).toList();
    } catch (error) {
      debugPrint('Failed to fetch cars: $error');
      return _getMockCars();
    }
  }

  // Get available cars only
  Future<List<CarModel>> getAvailableCars() async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockCars().where((c) => c.status == 'available').toList();
      }

      final response = await client
          .from('cars')
          .select('*')
          .eq('status', 'available')
          .order('car_model');

      return response.map<CarModel>((json) => CarModel.fromJson(json)).toList();
    } catch (error) {
      debugPrint('Failed to fetch available cars: $error');
      return _getMockCars().where((c) => c.status == 'available').toList();
    }
  }

  // Update car status
  Future<bool> updateCarStatus(String carId, String status) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        debugPrint('Car status updated (Preview Mode)');
        return true;
      }

      await client.from('cars').update({'status': status}).eq('id', carId);

      return true;
    } catch (error) {
      debugPrint('Failed to update car status: $error');
      return false;
    }
  }

  // Add new car
  Future<bool> addCar({
    required String carModel,
    required String licensePlate,
    int? year,
    String? color,
  }) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        debugPrint('Car added successfully (Preview Mode)');
        return true;
      }

      await client.from('cars').insert({
        'car_model': carModel,
        'license_plate': licensePlate,
        'year': year,
        'color': color,
        'status': 'available',
        'mileage': 0,
      });

      return true;
    } catch (error) {
      debugPrint('Failed to add car: $error');
      rethrow;
    }
  }

  // Get car by ID
  Future<CarModel?> getCarById(String carId) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockCars().where((c) => c.id == carId).firstOrNull;
      }

      final response =
          await client.from('cars').select('*').eq('id', carId).single();

      return CarModel.fromJson(response);
    } catch (error) {
      debugPrint('Failed to fetch car by ID: $error');
      return null;
    }
  }

  // Mock cars for fallback
  List<CarModel> _getMockCars() {
    return [
      CarModel(
        id: '1',
        carModel: 'Toyota Camry',
        licensePlate: 'ABC-123',
        status: 'available',
        year: 2022,
        color: 'White',
        mileage: 45000,
      ),
      CarModel(
        id: '2',
        carModel: 'Honda Accord',
        licensePlate: 'DEF-456',
        status: 'available',
        year: 2023,
        color: 'Black',
        mileage: 28000,
      ),
      CarModel(
        id: '3',
        carModel: 'Ford Transit',
        licensePlate: 'GHI-789',
        status: 'in_use',
        year: 2021,
        color: 'Blue',
        mileage: 67000,
      ),
      CarModel(
        id: '4',
        carModel: 'Nissan Sentra',
        licensePlate: 'JKL-012',
        status: 'available',
        year: 2022,
        color: 'Silver',
        mileage: 38000,
      ),
      CarModel(
        id: '5',
        carModel: 'Chevrolet Malibu',
        licensePlate: 'MNO-345',
        status: 'maintenance',
        year: 2020,
        color: 'Red',
        mileage: 85000,
      ),
    ];
  }
}
