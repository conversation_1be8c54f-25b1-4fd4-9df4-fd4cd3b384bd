import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../models/driver_model.dart';
import '../../services/driver_service.dart';
import './widgets/driver_card_widget.dart';
import './widgets/driver_detail_modal_widget.dart';
import './widgets/driver_search_bar_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/filter_chip_widget.dart';

class DriverListScreen extends StatefulWidget {
  const DriverListScreen({super.key});

  @override
  State<DriverListScreen> createState() => _DriverListScreenState();
}

class _DriverListScreenState extends State<DriverListScreen> {
  String _searchQuery = '';
  String _selectedFilter = 'all';
  bool _isLoading = true;
  bool _isRefreshing = false;

  final DriverService _driverService = DriverService();
  List<DriverModel> _allDrivers = [];

  @override
  void initState() {
    super.initState();
    _loadDrivers();
  }

  Future<void> _loadDrivers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final drivers = await _driverService.getAllDrivers();
      setState(() {
        _allDrivers = drivers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load drivers. Please try again.'),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
        ),
      );
    }
  }

  List<DriverModel> get _filteredDrivers {
    List<DriverModel> filtered = _allDrivers;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((driver) {
        final name = (driver.fullName ?? '').toLowerCase();
        return name.contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((driver) {
        return driver.status == _selectedFilter;
      }).toList();
    }

    return filtered;
  }

  int get _filterCount {
    if (_selectedFilter == 'all') return _allDrivers.length;
    return _allDrivers
        .where((driver) => driver.status == _selectedFilter)
        .length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Driver List',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 6.w,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _refreshDriverList,
            icon: _isRefreshing
                ? SizedBox(
                    width: 5.w,
                    height: 5.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                  )
                : CustomIconWidget(
                    iconName: 'refresh',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 6.w,
                  ),
          ),
          SizedBox(width: 2.w),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          DriverSearchBarWidget(
            searchQuery: _searchQuery,
            onSearchChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
            },
            onFilterTap: _showFilterBottomSheet,
          ),

          // Filter Chips
          if (_selectedFilter != 'all')
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    FilterChipWidget(
                      filterType: _selectedFilter,
                      count: _filterCount,
                      onRemove: () {
                        setState(() {
                          _selectedFilter = 'all';
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

          // Driver List
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                  )
                : _filteredDrivers.isEmpty
                    ? EmptyStateWidget(
                        filterType: _selectedFilter,
                        onClearFilter: _selectedFilter != 'all'
                            ? () {
                                setState(() {
                                  _selectedFilter = 'all';
                                  _searchQuery = '';
                                });
                              }
                            : null,
                        onRefresh: _refreshDriverList,
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshDriverList,
                        color: AppTheme.lightTheme.colorScheme.primary,
                        child: ListView.builder(
                          padding: EdgeInsets.only(
                            top: 1.h,
                            bottom: 2.h,
                          ),
                          itemCount: _filteredDrivers.length,
                          itemBuilder: (context, index) {
                            final driver = _filteredDrivers[index];
                            return DriverCardWidget(
                              driver: _convertDriverToMap(driver),
                              onTap: () => _showDriverDetail(driver),
                              onAssignMission: () => _assignMission(driver),
                              onCall: () => _callDriver(driver),
                              onMessage: () => _messageDriver(driver),
                              onViewHistory: () => _viewDriverHistory(driver),
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  // Convert DriverModel to Map for compatibility with existing widget
  Map<String, dynamic> _convertDriverToMap(DriverModel driver) {
    return {
      'id': driver.id,
      'name': driver.fullName ?? 'Unknown Driver',
      'status': driver.status,
      'lastActivity': driver.lastActivityText,
      'profileImage':
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png',
      'phone': driver.phone ?? 'N/A',
      'email': driver.email ?? 'N/A',
      'licenseNumber': driver.licenseNumber,
      'stats': {
        'totalMissions': driver.totalMissions,
        'completedMissions': driver.completedMissions,
        'rating': driver.rating,
        'onTimeDeliveries': driver.onTimeDeliveries,
      },
      'missionHistory': [], // This would need to be loaded separately
    };
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheetWidget(
        selectedFilter: _selectedFilter,
        onFilterChanged: (filter) {
          setState(() {
            _selectedFilter = filter;
          });
        },
      ),
    );
  }

  void _showDriverDetail(DriverModel driver) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DriverDetailModalWidget(
        driver: _convertDriverToMap(driver),
      ),
    );
  }

  Future<void> _refreshDriverList() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      await _loadDrivers();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Driver list updated'),
          backgroundColor: AppTheme.lightTheme.colorScheme.tertiary,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to refresh driver list'),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
        ),
      );
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  void _assignMission(DriverModel driver) {
    Navigator.pushNamed(context, '/create-mission-screen');
  }

  void _callDriver(DriverModel driver) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling ${driver.fullName ?? 'driver'}...'),
        action: SnackBarAction(
          label: 'Cancel',
          onPressed: () {},
        ),
      ),
    );
  }

  void _messageDriver(DriverModel driver) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening message to ${driver.fullName ?? 'driver'}...'),
      ),
    );
  }

  void _viewDriverHistory(DriverModel driver) {
    Navigator.pushNamed(context, '/mission-detail-screen');
  }
}
