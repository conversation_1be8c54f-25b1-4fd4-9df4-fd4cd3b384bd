import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class MissionActionButtons extends StatelessWidget {
  final Map<String, dynamic> missionData;
  final String userRole;
  final VoidCallback? onAcceptMission;
  final VoidCallback? onRejectMission;
  final VoidCallback? onStartMission;
  final VoidCallback? onCompleteMission;
  final VoidCallback? onContactDriver;
  final VoidCallback? onModifyMission;
  final VoidCallback? onCancelMission;

  const MissionActionButtons({
    Key? key,
    required this.missionData,
    required this.userRole,
    this.onAcceptMission,
    this.onRejectMission,
    this.onStartMission,
    this.onCompleteMission,
    this.onContactDriver,
    this.onModifyMission,
    this.onCancelMission,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final status = missionData['status'] as String? ?? 'pending';

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color:
                AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: userRole.toLowerCase() == 'driver'
          ? _buildDriverActions(status)
          : _buildManagerActions(status),
    );
  }

  Widget _buildDriverActions(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: onAcceptMission,
                icon: CustomIconWidget(
                  iconName: 'check_circle',
                  color: Colors.white,
                  size: 18,
                ),
                label: Text('Accept Mission'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.successLight,
                  padding: EdgeInsets.symmetric(vertical: 1.5.h),
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onRejectMission,
                icon: CustomIconWidget(
                  iconName: 'cancel',
                  color: AppTheme.errorLight,
                  size: 18,
                ),
                label: Text('Reject'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.errorLight,
                  side: BorderSide(color: AppTheme.errorLight),
                  padding: EdgeInsets.symmetric(vertical: 1.5.h),
                ),
              ),
            ),
          ],
        );

      case 'accepted':
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: onStartMission,
            icon: CustomIconWidget(
              iconName: 'play_arrow',
              color: Colors.white,
              size: 18,
            ),
            label: Text('Start Mission'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.lightTheme.primaryColor,
              padding: EdgeInsets.symmetric(vertical: 1.5.h),
            ),
          ),
        );

      case 'in-progress':
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: onCompleteMission,
            icon: CustomIconWidget(
              iconName: 'flag',
              color: Colors.white,
              size: 18,
            ),
            label: Text('Complete Mission'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.successLight,
              padding: EdgeInsets.symmetric(vertical: 1.5.h),
            ),
          ),
        );

      case 'completed':
        return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 1.5.h),
          decoration: BoxDecoration(
            color: AppTheme.successLight.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.successLight.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconWidget(
                iconName: 'check_circle',
                color: AppTheme.successLight,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Mission Completed',
                style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                  color: AppTheme.successLight,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );

      default:
        return SizedBox.shrink();
    }
  }

  Widget _buildManagerActions(String status) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onContactDriver,
                icon: CustomIconWidget(
                  iconName: 'phone',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 18,
                ),
                label: Text('Contact Driver'),
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 1.5.h),
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: status.toLowerCase() != 'completed'
                    ? onModifyMission
                    : null,
                icon: CustomIconWidget(
                  iconName: 'edit',
                  color: status.toLowerCase() != 'completed'
                      ? AppTheme.lightTheme.primaryColor
                      : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 18,
                ),
                label: Text('Modify'),
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 1.5.h),
                ),
              ),
            ),
          ],
        ),
        if (status.toLowerCase() != 'completed') ...[
          SizedBox(height: 2.h),
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              onPressed: onCancelMission,
              icon: CustomIconWidget(
                iconName: 'cancel',
                color: AppTheme.errorLight,
                size: 18,
              ),
              label: Text('Cancel Mission'),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.errorLight,
                padding: EdgeInsets.symmetric(vertical: 1.5.h),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
