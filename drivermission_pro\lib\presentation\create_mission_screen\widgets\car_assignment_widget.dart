import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class CarAssignmentWidget extends StatefulWidget {
  final Map<String, dynamic>? selectedCar;
  final Function(Map<String, dynamic>) onCarSelected;

  const CarAssignmentWidget({
    super.key,
    this.selectedCar,
    required this.onCarSelected,
  });

  @override
  State<CarAssignmentWidget> createState() => _CarAssignmentWidgetState();
}

class _CarAssignmentWidgetState extends State<CarAssignmentWidget> {
  bool _isDropdownOpen = false;

  final List<Map<String, dynamic>> _availableCars = [
    {
      "id": 1,
      "model": "Toyota Camry",
      "licensePlate": "ABC-1234",
      "fuelLevel": 85,
      "maintenanceStatus": "Good",
      "year": 2022,
      "color": "White",
      "image":
          "https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      "id": 2,
      "model": "Honda Accord",
      "licensePlate": "XYZ-5678",
      "fuelLevel": 92,
      "maintenanceStatus": "Excellent",
      "year": 2023,
      "color": "Silver",
      "image":
          "https://images.pexels.com/photos/3802510/pexels-photo-3802510.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      "id": 3,
      "model": "Ford Explorer",
      "licensePlate": "DEF-9012",
      "fuelLevel": 78,
      "maintenanceStatus": "Good",
      "year": 2021,
      "color": "Black",
      "image":
          "https://images.pexels.com/photos/1545743/pexels-photo-1545743.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      "id": 4,
      "model": "Chevrolet Malibu",
      "licensePlate": "GHI-3456",
      "fuelLevel": 67,
      "maintenanceStatus": "Fair",
      "year": 2020,
      "color": "Blue",
      "image":
          "https://images.pexels.com/photos/1149137/pexels-photo-1149137.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
  ];

  void _toggleDropdown() {
    setState(() {
      _isDropdownOpen = !_isDropdownOpen;
    });
  }

  void _selectCar(Map<String, dynamic> car) {
    widget.onCarSelected(car);
    setState(() {
      _isDropdownOpen = false;
    });
  }

  Color _getFuelLevelColor(int fuelLevel) {
    if (fuelLevel >= 75) {
      return AppTheme.lightTheme.colorScheme.tertiary;
    } else if (fuelLevel >= 50) {
      return Colors.orange;
    } else {
      return AppTheme.lightTheme.colorScheme.error;
    }
  }

  Color _getMaintenanceStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'excellent':
        return AppTheme.lightTheme.colorScheme.tertiary;
      case 'good':
        return Colors.blue;
      case 'fair':
        return Colors.orange;
      default:
        return AppTheme.lightTheme.colorScheme.error;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'directions_car',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Assign Vehicle',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  ' *',
                  style: TextStyle(
                    color: AppTheme.lightTheme.colorScheme.error,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            GestureDetector(
              onTap: _toggleDropdown,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppTheme.lightTheme.colorScheme.outline,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                  color: AppTheme.lightTheme.colorScheme.surface,
                ),
                child: Row(
                  children: [
                    widget.selectedCar != null
                        ? Expanded(
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: CustomImageWidget(
                                    imageUrl:
                                        widget.selectedCar!["image"] as String,
                                    width: 50,
                                    height: 35,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                SizedBox(width: 3.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${widget.selectedCar!["model"]} (${widget.selectedCar!["year"]})',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium
                                            ?.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        '${widget.selectedCar!["licensePlate"]} • ${widget.selectedCar!["color"]}',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodySmall,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Expanded(
                            child: Text(
                              'Choose an available vehicle',
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                color: AppTheme
                                    .lightTheme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                    CustomIconWidget(
                      iconName: _isDropdownOpen
                          ? 'keyboard_arrow_up'
                          : 'keyboard_arrow_down',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
            _isDropdownOpen ? SizedBox(height: 1.h) : const SizedBox.shrink(),
            _isDropdownOpen
                ? Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppTheme.lightTheme.colorScheme.outline,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(8.0),
                      color: AppTheme.lightTheme.colorScheme.surface,
                    ),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(maxHeight: 35.h),
                      child: ListView.separated(
                        shrinkWrap: true,
                        itemCount: _availableCars.length,
                        separatorBuilder: (context, index) => Divider(
                          height: 1,
                          color: AppTheme.lightTheme.colorScheme.outline,
                        ),
                        itemBuilder: (context, index) {
                          final car = _availableCars[index];
                          return ListTile(
                            onTap: () => _selectCar(car),
                            contentPadding: EdgeInsets.all(3.w),
                            leading: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CustomImageWidget(
                                imageUrl: car["image"] as String,
                                width: 60,
                                height: 45,
                                fit: BoxFit.cover,
                              ),
                            ),
                            title: Text(
                              '${car["model"]} (${car["year"]})',
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 0.5.h),
                                Text(
                                  '${car["licensePlate"]} • ${car["color"]}',
                                  style:
                                      AppTheme.lightTheme.textTheme.bodySmall,
                                ),
                                SizedBox(height: 1.h),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Row(
                                        children: [
                                          CustomIconWidget(
                                            iconName: 'local_gas_station',
                                            color: _getFuelLevelColor(
                                                car["fuelLevel"] as int),
                                            size: 14,
                                          ),
                                          SizedBox(width: 1.w),
                                          Text(
                                            '${car["fuelLevel"]}%',
                                            style: AppTheme
                                                .lightTheme.textTheme.bodySmall
                                                ?.copyWith(
                                              color: _getFuelLevelColor(
                                                  car["fuelLevel"] as int),
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Row(
                                      children: [
                                        CustomIconWidget(
                                          iconName: 'build',
                                          color: _getMaintenanceStatusColor(
                                              car["maintenanceStatus"]
                                                  as String),
                                          size: 14,
                                        ),
                                        SizedBox(width: 1.w),
                                        Text(
                                          car["maintenanceStatus"] as String,
                                          style: AppTheme
                                              .lightTheme.textTheme.bodySmall
                                              ?.copyWith(
                                            color: _getMaintenanceStatusColor(
                                                car["maintenanceStatus"]
                                                    as String),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            trailing: CustomIconWidget(
                              iconName: 'check_circle_outline',
                              color: AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                              size: 20,
                            ),
                          );
                        },
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
