-- Driver Mission Management System - Complete Database Schema
-- Migration: 20250119185600_driver_mission_system.sql

-- ====================================================================
-- 1. CUSTOM TYPES
-- ====================================================================

CREATE TYPE public.user_role AS ENUM ('admin', 'manager', 'driver');
CREATE TYPE public.driver_status AS ENUM ('available', 'unavailable', 'on_mission', 'on_leave');
CREATE TYPE public.mission_status AS ENUM ('pending', 'accepted', 'in_progress', 'completed', 'cancelled');
CREATE TYPE public.car_status AS ENUM ('available', 'in_use', 'maintenance', 'out_of_service');

-- ====================================================================
-- 2. CORE TABLES
-- ====================================================================

-- User profiles table (intermediary for auth.users)
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    role public.user_role DEFAULT 'driver'::public.user_role,
    phone TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Drivers table
CREATE TABLE public.drivers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    license_number TEXT NOT NULL UNIQUE,
    status public.driver_status DEFAULT 'available'::public.driver_status,
    status_reason TEXT,
    last_activity TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    total_missions INTEGER DEFAULT 0,
    completed_missions INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    on_time_deliveries INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Cars table  
CREATE TABLE public.cars (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    car_model TEXT NOT NULL,
    license_plate TEXT NOT NULL UNIQUE,
    status public.car_status DEFAULT 'available'::public.car_status,
    year INTEGER,
    color TEXT,
    mileage INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Missions table
CREATE TABLE public.missions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mission_object TEXT NOT NULL,
    destination TEXT NOT NULL,
    scheduled_date TIMESTAMPTZ NOT NULL,
    driver_id UUID REFERENCES public.drivers(id) ON DELETE SET NULL,
    car_id UUID REFERENCES public.cars(id) ON DELETE SET NULL,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    status public.mission_status DEFAULT 'pending'::public.mission_status,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMPTZ,
    accepted_at TIMESTAMPTZ
);

-- ====================================================================
-- 3. INDEXES
-- ====================================================================

CREATE INDEX idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_drivers_user_id ON public.drivers(user_id);
CREATE INDEX idx_drivers_status ON public.drivers(status);
CREATE INDEX idx_drivers_license ON public.drivers(license_number);
CREATE INDEX idx_cars_status ON public.cars(status);
CREATE INDEX idx_cars_license_plate ON public.cars(license_plate);
CREATE INDEX idx_missions_driver_id ON public.missions(driver_id);
CREATE INDEX idx_missions_car_id ON public.missions(car_id);
CREATE INDEX idx_missions_status ON public.missions(status);
CREATE INDEX idx_missions_created_by ON public.missions(created_by);
CREATE INDEX idx_missions_scheduled_date ON public.missions(scheduled_date);

-- ====================================================================
-- 4. ROW LEVEL SECURITY SETUP
-- ====================================================================

ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.missions ENABLE ROW LEVEL SECURITY;

-- ====================================================================
-- 5. HELPER FUNCTIONS FOR RLS
-- ====================================================================

-- Check if user has a specific role
CREATE OR REPLACE FUNCTION public.has_role(required_role TEXT)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role::TEXT = required_role
);
$$;

-- Check if user is manager or admin
CREATE OR REPLACE FUNCTION public.is_manager_or_admin()
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role IN ('manager', 'admin')
);
$$;

-- Check if user can access driver data
CREATE OR REPLACE FUNCTION public.can_access_driver_data(driver_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT 
    public.is_manager_or_admin() OR
    EXISTS (
        SELECT 1 FROM public.drivers d
        WHERE d.id = driver_uuid AND d.user_id = auth.uid()
    );
$$;

-- Check if user can access mission
CREATE OR REPLACE FUNCTION public.can_access_mission(mission_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT 
    public.is_manager_or_admin() OR
    EXISTS (
        SELECT 1 FROM public.missions m
        JOIN public.drivers d ON m.driver_id = d.id
        WHERE m.id = mission_uuid AND d.user_id = auth.uid()
    );
$$;

-- ====================================================================
-- 6. RLS POLICIES
-- ====================================================================

-- User profiles: Users can only see/edit their own profile, managers can see all
CREATE POLICY "users_own_profile" ON public.user_profiles FOR ALL
TO authenticated
USING (auth.uid() = id OR public.is_manager_or_admin())
WITH CHECK (auth.uid() = id OR public.is_manager_or_admin());

-- Drivers: Drivers can see own data, managers can see all
CREATE POLICY "drivers_access_control" ON public.drivers FOR ALL
TO authenticated
USING (public.can_access_driver_data(id))
WITH CHECK (public.can_access_driver_data(id));

-- Cars: Only managers can manage cars, drivers can view assigned cars
CREATE POLICY "managers_manage_cars" ON public.cars FOR ALL
TO authenticated
USING (public.is_manager_or_admin())
WITH CHECK (public.is_manager_or_admin());

-- Missions: Managers can manage all, drivers can see assigned missions
CREATE POLICY "missions_access_control" ON public.missions FOR ALL
TO authenticated
USING (public.can_access_mission(id))
WITH CHECK (public.can_access_mission(id));

-- ====================================================================
-- 7. TRIGGERS & FUNCTIONS
-- ====================================================================

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, full_name, role)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'role', 'driver')::public.user_role
    );
    RETURN NEW;
END;
$$;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_drivers_updated_at
    BEFORE UPDATE ON public.drivers
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_cars_updated_at
    BEFORE UPDATE ON public.cars
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_missions_updated_at
    BEFORE UPDATE ON public.missions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

-- ====================================================================
-- 8. MOCK DATA
-- ====================================================================

DO $$
DECLARE
    admin_uuid UUID := gen_random_uuid();
    manager1_uuid UUID := gen_random_uuid();
    driver1_uuid UUID := gen_random_uuid();
    driver2_uuid UUID := gen_random_uuid();
    driver3_uuid UUID := gen_random_uuid();
    driver4_uuid UUID := gen_random_uuid();
    driver5_uuid UUID := gen_random_uuid();
    driver6_uuid UUID := gen_random_uuid();
    
    driver1_id UUID := gen_random_uuid();
    driver2_id UUID := gen_random_uuid();
    driver3_id UUID := gen_random_uuid();
    driver4_id UUID := gen_random_uuid();
    driver5_id UUID := gen_random_uuid();
    driver6_id UUID := gen_random_uuid();
    
    car1_id UUID := gen_random_uuid();
    car2_id UUID := gen_random_uuid();
    car3_id UUID := gen_random_uuid();
    car4_id UUID := gen_random_uuid();
    car5_id UUID := gen_random_uuid();
BEGIN
    -- Create auth users with required fields
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (admin_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Admin123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "System Administrator", "role": "admin"}'::jsonb, 
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (manager1_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Manager123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "John Manager", "role": "manager"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (driver1_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Driver123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Michael Rodriguez", "role": "driver"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (driver2_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Driver123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Sarah Johnson", "role": "driver"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (driver3_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Driver123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "David Chen", "role": "driver"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (driver4_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Driver123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Emily Davis", "role": "driver"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (driver5_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Driver123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "James Wilson", "role": "driver"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (driver6_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('Driver123!', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Lisa Thompson", "role": "driver"}'::jsonb,
         '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Insert cars data
    INSERT INTO public.cars (id, car_model, license_plate, status, year, color) VALUES
        (car1_id, 'Toyota Camry', 'ABC-123', 'available'::public.car_status, 2022, 'White'),
        (car2_id, 'Honda Accord', 'DEF-456', 'available'::public.car_status, 2023, 'Black'),
        (car3_id, 'Ford Transit', 'GHI-789', 'in_use'::public.car_status, 2021, 'Blue'),
        (car4_id, 'Nissan Sentra', 'JKL-012', 'available'::public.car_status, 2022, 'Silver'),
        (car5_id, 'Chevrolet Malibu', 'MNO-345', 'maintenance'::public.car_status, 2020, 'Red');

    -- Insert drivers data
    INSERT INTO public.drivers (id, user_id, license_number, status, total_missions, completed_missions, rating, on_time_deliveries) VALUES
        (driver1_id, driver1_uuid, 'DL-2024-001', 'available'::public.driver_status, 156, 148, 4.8, 142),
        (driver2_id, driver2_uuid, 'DL-2024-002', 'on_mission'::public.driver_status, 203, 195, 4.9, 188),
        (driver3_id, driver3_uuid, 'DL-2024-003', 'available'::public.driver_status, 89, 84, 4.7, 79),
        (driver4_id, driver4_uuid, 'DL-2024-004', 'unavailable'::public.driver_status, 134, 128, 4.6, 121),
        (driver5_id, driver5_uuid, 'DL-2024-005', 'available'::public.driver_status, 178, 169, 4.8, 162),
        (driver6_id, driver6_uuid, 'DL-2024-006', 'on_mission'::public.driver_status, 145, 138, 4.7, 131);

    -- Insert sample missions
    INSERT INTO public.missions (mission_object, destination, scheduled_date, driver_id, car_id, created_by, status) VALUES
        ('Deliver documents to client office', 'Downtown Office Complex', now() + interval '2 hours', driver1_id, car1_id, manager1_uuid, 'pending'::public.mission_status),
        ('Transport equipment to warehouse', 'Industrial District', now() + interval '1 day', driver2_id, car3_id, manager1_uuid, 'in_progress'::public.mission_status),
        ('Pick up supplies from vendor', 'Shopping Mall Plaza', now() + interval '3 hours', driver3_id, car2_id, manager1_uuid, 'accepted'::public.mission_status),
        ('Deliver notification to branch office', 'City Center Plaza', now() + interval '6 hours', driver5_id, car4_id, manager1_uuid, 'pending'::public.mission_status);

EXCEPTION
    WHEN unique_violation THEN
        RAISE NOTICE 'Some data already exists, skipping duplicates';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting mock data: %', SQLERRM;
END $$;

-- ====================================================================
-- 9. CLEANUP FUNCTION (FOR TESTING)
-- ====================================================================

CREATE OR REPLACE FUNCTION public.cleanup_test_data()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    auth_user_ids_to_delete UUID[];
BEGIN
    -- Get auth user IDs to delete
    SELECT ARRAY_AGG(id) INTO auth_user_ids_to_delete
    FROM auth.users
    WHERE email LIKE '%@drivermission.com';

    -- Delete in dependency order
    DELETE FROM public.missions WHERE created_by = ANY(auth_user_ids_to_delete);
    DELETE FROM public.drivers WHERE user_id = ANY(auth_user_ids_to_delete);
    DELETE FROM public.user_profiles WHERE id = ANY(auth_user_ids_to_delete);
    DELETE FROM auth.users WHERE id = ANY(auth_user_ids_to_delete);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Cleanup failed: %', SQLERRM;
END;
$$;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;