class DriverModel {
  final String id;
  final String userId;
  final String licenseNumber;
  final String status;
  final String? statusReason;
  final DateTime? lastActivity;
  final int totalMissions;
  final int completedMissions;
  final double rating;
  final int onTimeDeliveries;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // User profile data
  final String? fullName;
  final String? email;
  final String? phone;

  const DriverModel({
    required this.id,
    required this.userId,
    required this.licenseNumber,
    required this.status,
    this.statusReason,
    this.lastActivity,
    required this.totalMissions,
    required this.completedMissions,
    required this.rating,
    required this.onTimeDeliveries,
    this.createdAt,
    this.updatedAt,
    this.fullName,
    this.email,
    this.phone,
  });

  factory DriverModel.fromJson(Map<String, dynamic> json) {
    final userProfile = json['user_profiles'];

    return DriverModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      licenseNumber: json['license_number'] as String,
      status: json['status'] as String,
      statusReason: json['status_reason'] as String?,
      lastActivity: json['last_activity'] != null
          ? DateTime.parse(json['last_activity'] as String)
          : null,
      totalMissions: json['total_missions'] as int? ?? 0,
      completedMissions: json['completed_missions'] as int? ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      onTimeDeliveries: json['on_time_deliveries'] as int? ?? 0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      fullName: userProfile?['full_name'] as String?,
      email: userProfile?['email'] as String?,
      phone: userProfile?['phone'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'license_number': licenseNumber,
      'status': status,
      'status_reason': statusReason,
      'last_activity': lastActivity?.toIso8601String(),
      'total_missions': totalMissions,
      'completed_missions': completedMissions,
      'rating': rating,
      'on_time_deliveries': onTimeDeliveries,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Convenience getters
  String get displayStatus {
    switch (status) {
      case 'available':
        return 'Available';
      case 'unavailable':
        return 'Unavailable';
      case 'on_mission':
        return 'On Mission';
      case 'on_leave':
        return 'On Leave';
      default:
        return status;
    }
  }

  String get lastActivityText {
    if (lastActivity == null) return 'Unknown';

    final difference = DateTime.now().difference(lastActivity!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  double get completionRate {
    if (totalMissions == 0) return 0.0;
    return (completedMissions / totalMissions) * 100;
  }

  double get onTimeRate {
    if (completedMissions == 0) return 0.0;
    return (onTimeDeliveries / completedMissions) * 100;
  }

  DriverModel copyWith({
    String? id,
    String? userId,
    String? licenseNumber,
    String? status,
    String? statusReason,
    DateTime? lastActivity,
    int? totalMissions,
    int? completedMissions,
    double? rating,
    int? onTimeDeliveries,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? fullName,
    String? email,
    String? phone,
  }) {
    return DriverModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      status: status ?? this.status,
      statusReason: statusReason ?? this.statusReason,
      lastActivity: lastActivity ?? this.lastActivity,
      totalMissions: totalMissions ?? this.totalMissions,
      completedMissions: completedMissions ?? this.completedMissions,
      rating: rating ?? this.rating,
      onTimeDeliveries: onTimeDeliveries ?? this.onTimeDeliveries,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
    );
  }
}
