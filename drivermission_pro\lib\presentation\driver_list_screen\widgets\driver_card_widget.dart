import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DriverCardWidget extends StatelessWidget {
  final Map<String, dynamic> driver;
  final VoidCallback? onTap;
  final VoidCallback? onAssignMission;
  final VoidCallback? onCall;
  final VoidCallback? onMessage;
  final VoidCallback? onViewHistory;

  const DriverCardWidget({
    super.key,
    required this.driver,
    this.onTap,
    this.onAssignMission,
    this.onCall,
    this.onMessage,
    this.onViewHistory,
  });

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return AppTheme.lightTheme.colorScheme.tertiary;
      case 'on_mission':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'unavailable':
        return AppTheme.lightTheme.colorScheme.error;
      default:
        return AppTheme.lightTheme.colorScheme.secondary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return 'Available';
      case 'on_mission':
        return 'On Mission';
      case 'unavailable':
        return 'Unavailable';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    final String name = driver['name'] ?? 'Unknown Driver';
    final String status = driver['status'] ?? 'unknown';
    final String lastActivity = driver['lastActivity'] ?? 'No recent activity';
    final String profileImage = driver['profileImage'] ?? '';
    final bool isAvailable = status.toLowerCase() == 'available';

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Row(
            children: [
              // Profile Image
              Container(
                width: 15.w,
                height: 15.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _getStatusColor(status),
                    width: 2,
                  ),
                ),
                child: ClipOval(
                  child: profileImage.isNotEmpty
                      ? CustomImageWidget(
                          imageUrl: profileImage,
                          width: 15.w,
                          height: 15.w,
                          fit: BoxFit.cover,
                        )
                      : Container(
                          color: AppTheme.lightTheme.colorScheme.surface,
                          child: CustomIconWidget(
                            iconName: 'person',
                            color: AppTheme.lightTheme.colorScheme.secondary,
                            size: 8.w,
                          ),
                        ),
                ),
              ),
              SizedBox(width: 4.w),

              // Driver Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            name,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 2.w,
                            vertical: 0.5.h,
                          ),
                          decoration: BoxDecoration(
                            color:
                                _getStatusColor(status).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _getStatusColor(status),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            _getStatusText(status),
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color: _getStatusColor(status),
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      lastActivity,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme
                                .lightTheme.colorScheme.onSurfaceVariant,
                          ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Action Buttons
              if (isAvailable) ...[
                SizedBox(width: 2.w),
                ElevatedButton(
                  onPressed: onAssignMission,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: 3.w,
                      vertical: 1.h,
                    ),
                    minimumSize: Size(0, 5.h),
                  ),
                  child: Text(
                    'Assign',
                    style: TextStyle(fontSize: 11.sp),
                  ),
                ),
              ],

              // More Options
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'call':
                      onCall?.call();
                      break;
                    case 'message':
                      onMessage?.call();
                      break;
                    case 'history':
                      onViewHistory?.call();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'call',
                    child: Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'phone',
                          color: AppTheme.lightTheme.colorScheme.primary,
                          size: 5.w,
                        ),
                        SizedBox(width: 2.w),
                        Text('Call'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'message',
                    child: Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'message',
                          color: AppTheme.lightTheme.colorScheme.primary,
                          size: 5.w,
                        ),
                        SizedBox(width: 2.w),
                        Text('Message'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'history',
                    child: Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'history',
                          color: AppTheme.lightTheme.colorScheme.primary,
                          size: 5.w,
                        ),
                        SizedBox(width: 2.w),
                        Text('View History'),
                      ],
                    ),
                  ),
                ],
                child: Padding(
                  padding: EdgeInsets.all(1.w),
                  child: CustomIconWidget(
                    iconName: 'more_vert',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 5.w,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
