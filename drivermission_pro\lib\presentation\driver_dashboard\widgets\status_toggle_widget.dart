import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class StatusToggleWidget extends StatefulWidget {
  final bool isAvailable;
  final Function(bool) onStatusChanged;

  const StatusToggleWidget({
    Key? key,
    required this.isAvailable,
    required this.onStatusChanged,
  }) : super(key: key);

  @override
  State<StatusToggleWidget> createState() => _StatusToggleWidgetState();
}

class _StatusToggleWidgetState extends State<StatusToggleWidget> {
  void _showUnavailabilityReasons() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Unavailability Reason',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 3.h),
            _buildReasonTile('Break', 'coffee'),
            _buildReasonTile('Maintenance', 'build'),
            _buildReasonTile('Emergency', 'warning'),
            _buildReasonTile('End of Shift', 'schedule'),
            SizedBox(height: 2.h),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReasonTile(String reason, String iconName) {
    return ListTile(
      leading: CustomIconWidget(
        iconName: iconName,
        color: AppTheme.lightTheme.colorScheme.secondary,
        size: 24,
      ),
      title: Text(reason),
      onTap: () {
        Navigator.pop(context);
        widget.onStatusChanged(false);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: widget.isAvailable
            ? AppTheme.lightTheme.colorScheme.tertiary.withValues(alpha: 0.1)
            : AppTheme.lightTheme.colorScheme.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: widget.isAvailable
              ? AppTheme.lightTheme.colorScheme.tertiary
              : AppTheme.lightTheme.colorScheme.error,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomIconWidget(
            iconName: widget.isAvailable ? 'check_circle' : 'cancel',
            color: widget.isAvailable
                ? AppTheme.lightTheme.colorScheme.tertiary
                : AppTheme.lightTheme.colorScheme.error,
            size: 16,
          ),
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: widget.isAvailable
                ? _showUnavailabilityReasons
                : () => widget.onStatusChanged(true),
            child: Text(
              widget.isAvailable ? 'Available' : 'Unavailable',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: widget.isAvailable
                        ? AppTheme.lightTheme.colorScheme.tertiary
                        : AppTheme.lightTheme.colorScheme.error,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
