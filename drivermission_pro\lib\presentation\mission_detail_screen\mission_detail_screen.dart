import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/assignment_details_card.dart';
import './widgets/destination_card.dart';
import './widgets/mission_action_buttons.dart';
import './widgets/mission_status_badge.dart';
import './widgets/schedule_card.dart';
import './widgets/status_timeline_card.dart';

class MissionDetailScreen extends StatefulWidget {
  const MissionDetailScreen({Key? key}) : super(key: key);

  @override
  State<MissionDetailScreen> createState() => _MissionDetailScreenState();
}

class _MissionDetailScreenState extends State<MissionDetailScreen> {
  late Map<String, dynamic> missionData;
  String userRole = 'driver'; // This would come from authentication/session
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeMissionData();
  }

  void _initializeMissionData() {
    // Mock mission data - in real app this would come from API/database
    missionData = {
      "id": "MSN-2025-001",
      "title": "Downtown Delivery Mission",
      "status": "in-progress",
      "priority": "high",
      "createdAt": "2025-07-19T10:30:00Z",
      "driver": {
        "id": "DRV-001",
        "name": "Michael Rodriguez",
        "phone": "+****************",
        "avatar":
            "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
        "status": "available"
      },
      "vehicle": {
        "id": "VEH-001",
        "model": "Ford Transit Van",
        "licensePlate": "ABC-1234",
        "type": "delivery_van"
      },
      "destination": {
        "address":
            "123 Main Street, Downtown Business District, New York, NY 10001",
        "coordinates": {"latitude": 40.7128, "longitude": -74.0060},
        "contactPerson": "Sarah Johnson",
        "contactPhone": "+****************"
      },
      "schedule": {
        "pickupTime": "Today, 2:30 PM",
        "estimatedDuration": "2 hours 30 minutes",
        "deadline": "Today, 5:00 PM",
        "actualStartTime": "2:30 PM",
        "estimatedArrival": "5:00 PM"
      },
      "timeline": [
        {
          "status": "Mission Created",
          "timestamp": "Jul 19, 2025 - 10:30 AM",
          "description": "Mission assigned to driver by fleet manager",
          "completed": true,
        },
        {
          "status": "Mission Assigned",
          "timestamp": "Jul 19, 2025 - 10:35 AM",
          "description": "Driver received mission notification",
          "completed": true,
        },
        {
          "status": "Mission Accepted",
          "timestamp": "Jul 19, 2025 - 11:00 AM",
          "description": "Driver accepted the mission assignment",
          "completed": true,
        },
        {
          "status": "Mission Started",
          "timestamp": "Jul 19, 2025 - 2:30 PM",
          "description": "Driver started the mission and began route",
          "completed": true,
        },
        {
          "status": "In Progress",
          "timestamp": "Jul 19, 2025 - 3:45 PM",
          "description": "Mission currently in progress",
          "completed": false,
        },
        {
          "status": "Mission Completed",
          "timestamp": "Pending",
          "description": "Mission completion pending",
          "completed": false,
        },
      ],
      "notes":
          "Handle with care - fragile items included. Customer prefers delivery at rear entrance.",
      "estimatedDistance": "15.2 miles",
      "estimatedFuelCost": "\$12.50"
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: MissionActionButtons(
        missionData: missionData,
        userRole: userRole,
        onAcceptMission: _handleAcceptMission,
        onRejectMission: _handleRejectMission,
        onStartMission: _handleStartMission,
        onCompleteMission: _handleCompleteMission,
        onContactDriver: _handleContactDriver,
        onModifyMission: _handleModifyMission,
        onCancelMission: _handleCancelMission,
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.lightTheme.colorScheme.surface,
      elevation: 1,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: CustomIconWidget(
          iconName: 'arrow_back',
          color: AppTheme.lightTheme.colorScheme.onSurface,
          size: 24,
        ),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            missionData['title'] as String? ?? 'Mission Details',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            'ID: ${missionData['id'] as String? ?? 'N/A'}',
            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      actions: [
        Container(
          margin: EdgeInsets.only(right: 4.w),
          child: MissionStatusBadge(
            status: missionData['status'] as String? ?? 'pending',
            isDarkMode: false,
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: AppTheme.lightTheme.primaryColor,
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 2.h),
          AssignmentDetailsCard(missionData: missionData),
          DestinationCard(missionData: missionData),
          ScheduleCard(missionData: missionData),
          StatusTimelineCard(missionData: missionData),
          _buildAdditionalInfo(),
          SizedBox(height: 2.h),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    final notes = missionData['notes'] as String? ?? '';
    final distance = missionData['estimatedDistance'] as String? ?? '';
    final fuelCost = missionData['estimatedFuelCost'] as String? ?? '';

    if (notes.isEmpty && distance.isEmpty && fuelCost.isEmpty) {
      return SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'info',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Additional Information',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            if (distance.isNotEmpty || fuelCost.isNotEmpty) ...[
              Row(
                children: [
                  if (distance.isNotEmpty) ...[
                    Expanded(
                      child: _buildInfoItem('Distance', distance, 'straighten'),
                    ),
                  ],
                  if (distance.isNotEmpty && fuelCost.isNotEmpty)
                    SizedBox(width: 4.w),
                  if (fuelCost.isNotEmpty) ...[
                    Expanded(
                      child: _buildInfoItem(
                          'Est. Fuel Cost', fuelCost, 'local_gas_station'),
                    ),
                  ],
                ],
              ),
              SizedBox(height: 2.h),
            ],
            if (notes.isNotEmpty) ...[
              _buildNotesSection(notes),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, String iconName) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: iconName,
            color: AppTheme.lightTheme.primaryColor,
            size: 16,
          ),
          SizedBox(width: 2.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(String notes) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: AppTheme.warningLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.warningLight.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'note',
                color: AppTheme.warningLight,
                size: 16,
              ),
              SizedBox(width: 2.w),
              Text(
                'Special Notes',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.warningLight,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Text(
            notes,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  // Action handlers
  void _handleAcceptMission() {
    setState(() {
      isLoading = true;
    });

    // Simulate API call
    Future.delayed(Duration(seconds: 1), () {
      setState(() {
        missionData['status'] = 'accepted';
        isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Mission accepted successfully'),
          backgroundColor: AppTheme.successLight,
          behavior: SnackBarBehavior.floating,
        ),
      );
    });
  }

  void _handleRejectMission() {
    _showRejectDialog();
  }

  void _handleStartMission() {
    setState(() {
      isLoading = true;
    });

    Future.delayed(Duration(seconds: 1), () {
      setState(() {
        missionData['status'] = 'in-progress';
        isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Mission started successfully'),
          backgroundColor: AppTheme.lightTheme.primaryColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    });
  }

  void _handleCompleteMission() {
    setState(() {
      isLoading = true;
    });

    Future.delayed(Duration(seconds: 1), () {
      setState(() {
        missionData['status'] = 'completed';
        isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Mission completed successfully'),
          backgroundColor: AppTheme.successLight,
          behavior: SnackBarBehavior.floating,
        ),
      );
    });
  }

  void _handleContactDriver() {
    final driverPhone =
        (missionData['driver'] as Map<String, dynamic>?)?['phone'] as String? ??
            '';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling driver at $driverPhone...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _handleModifyMission() {
    Navigator.pushNamed(context, '/create-mission-screen');
  }

  void _handleCancelMission() {
    _showCancelDialog();
  }

  void _showRejectDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Reject Mission'),
          content: Text(
              'Are you sure you want to reject this mission? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  missionData['status'] = 'rejected';
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Mission rejected'),
                    backgroundColor: AppTheme.errorLight,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorLight,
              ),
              child: Text('Reject'),
            ),
          ],
        );
      },
    );
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Cancel Mission'),
          content: Text(
              'Are you sure you want to cancel this mission? This will notify the driver immediately.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Keep Mission'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  missionData['status'] = 'cancelled';
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Mission cancelled successfully'),
                    backgroundColor: AppTheme.errorLight,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorLight,
              ),
              child: Text('Cancel Mission'),
            ),
          ],
        );
      },
    );
  }
}
