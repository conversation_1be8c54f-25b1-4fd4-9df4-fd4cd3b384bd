import 'package:flutter/material.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/driver_dashboard/driver_dashboard.dart';
import '../presentation/manager_dashboard/manager_dashboard.dart';
import '../presentation/driver_list_screen/driver_list_screen.dart';
import '../presentation/mission_detail_screen/mission_detail_screen.dart';
import '../presentation/create_mission_screen/create_mission_screen.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String loginScreen = '/login-screen';
  static const String driverDashboard = '/driver-dashboard';
  static const String managerDashboard = '/manager-dashboard';
  static const String driverListScreen = '/driver-list-screen';
  static const String missionDetailScreen = '/mission-detail-screen';
  static const String createMissionScreen = '/create-mission-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => LoginScreen(),
    loginScreen: (context) => LoginScreen(),
    driverDashboard: (context) => DriverDashboard(),
    managerDashboard: (context) => ManagerDashboard(),
    driverListScreen: (context) => DriverListScreen(),
    missionDetailScreen: (context) => MissionDetailScreen(),
    createMissionScreen: (context) => CreateMissionScreen(),
    // TODO: Add your other routes here
  };
}
