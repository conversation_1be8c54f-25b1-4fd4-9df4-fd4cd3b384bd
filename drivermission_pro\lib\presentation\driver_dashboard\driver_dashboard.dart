import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/current_mission_card_widget.dart';
import './widgets/notification_badge_widget.dart';
import './widgets/status_toggle_widget.dart';
import './widgets/upcoming_missions_widget.dart';

class DriverDashboard extends StatefulWidget {
  const DriverDashboard({Key? key}) : super(key: key);

  @override
  State<DriverDashboard> createState() => _DriverDashboardState();
}

class _DriverDashboardState extends State<DriverDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isAvailable = true;
  int _notificationCount = 3;
  bool _isRefreshing = false;

  // Mock data for current mission
  Map<String, dynamic>? _currentMission = {
    "id": 1,
    "destination": "Downtown Distribution Center, 1234 Main Street",
    "estimatedTime": "45 minutes",
    "vehicle": "Truck #TK-2024",
    "status": "pending",
    "assignedBy": "<PERSON> <PERSON>",
    "scheduledTime": "2:30 PM",
    "priority": "HIGH"
  };

  // Mock data for upcoming missions
  final List<Map<String, dynamic>> _upcomingMissions = [
    {
      "id": 2,
      "destination": "Warehouse Complex B, Industrial Park",
      "estimatedTime": "30 minutes",
      "vehicle": "Van #VN-2024",
      "status": "scheduled",
      "assignedBy": "Manager Sarah Wilson",
      "scheduledTime": "4:00 PM",
      "priority": "MEDIUM"
    },
    {
      "id": 3,
      "destination": "Client Office Tower, Business District",
      "estimatedTime": "25 minutes",
      "vehicle": "Truck #TK-2025",
      "status": "scheduled",
      "assignedBy": "Manager Mike Johnson",
      "scheduledTime": "5:30 PM",
      "priority": "LOW"
    },
    {
      "id": 4,
      "destination": "Retail Store Chain, Shopping Mall",
      "estimatedTime": "40 minutes",
      "vehicle": "Van #VN-2025",
      "status": "scheduled",
      "assignedBy": "Manager Lisa Brown",
      "scheduledTime": "7:00 PM",
      "priority": "HIGH"
    }
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refreshData() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    setState(() {
      _isRefreshing = false;
      _notificationCount = 2; // Simulate updated notification count
    });

    Fluttertoast.showToast(
      msg: "Mission data refreshed",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void _handleStatusChange(bool isAvailable) {
    setState(() {
      _isAvailable = isAvailable;
    });

    Fluttertoast.showToast(
      msg: isAvailable ? "Status: Available" : "Status: Unavailable",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void _handleMissionAction(String action) {
    switch (action) {
      case 'accept':
        setState(() {
          if (_currentMission != null) {
            _currentMission!['status'] = 'in_progress';
          }
        });
        Fluttertoast.showToast(
          msg: "Mission accepted and started",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        break;
      case 'reject':
        setState(() {
          _currentMission = null;
        });
        Fluttertoast.showToast(
          msg: "Mission rejected",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        break;
      case 'complete':
        setState(() {
          if (_currentMission != null) {
            _currentMission!['status'] = 'completed';
          }
        });
        Fluttertoast.showToast(
          msg: "Mission completed successfully",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        break;
    }
  }

  void _handleUpcomingMissionTap(Map<String, dynamic> mission) {
    Navigator.pushNamed(context, '/mission-detail-screen');
  }

  void _handleUpcomingMissionAction(
      Map<String, dynamic> mission, String action) {
    switch (action) {
      case 'view_details':
        Navigator.pushNamed(context, '/mission-detail-screen');
        break;
      case 'contact_manager':
        Fluttertoast.showToast(
          msg: "Contacting ${mission['assignedBy']}...",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        break;
      case 'report_issue':
        Fluttertoast.showToast(
          msg: "Issue report submitted",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        break;
    }
  }

  void _handleNotificationTap() {
    setState(() {
      _notificationCount = 0;
    });
    Fluttertoast.showToast(
      msg: "Notifications viewed",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome back,',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.secondary,
                  ),
            ),
            Text(
              'David Rodriguez',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
            ),
          ],
        ),
        actions: [
          StatusToggleWidget(
            isAvailable: _isAvailable,
            onStatusChanged: _handleStatusChange,
          ),
          SizedBox(width: 3.w),
          NotificationBadgeWidget(
            notificationCount: _notificationCount,
            onTap: _handleNotificationTap,
          ),
          SizedBox(width: 4.w),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: CustomIconWidget(
                iconName: 'dashboard',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 20,
              ),
              text: 'Dashboard',
            ),
            Tab(
              icon: CustomIconWidget(
                iconName: 'assignment',
                color: AppTheme.lightTheme.colorScheme.secondary,
                size: 20,
              ),
              text: 'Missions',
            ),
            Tab(
              icon: CustomIconWidget(
                iconName: 'history',
                color: AppTheme.lightTheme.colorScheme.secondary,
                size: 20,
              ),
              text: 'History',
            ),
            Tab(
              icon: CustomIconWidget(
                iconName: 'person',
                color: AppTheme.lightTheme.colorScheme.secondary,
                size: 20,
              ),
              text: 'Profile',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDashboardTab(),
          _buildMissionsTab(),
          _buildHistoryTab(),
          _buildProfileTab(),
        ],
      ),
    );
  }

  Widget _buildDashboardTab() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 2.h),
            CurrentMissionCardWidget(
              currentMission: _currentMission,
              onMissionAction: _handleMissionAction,
            ),
            SizedBox(height: 2.h),
            UpcomingMissionsWidget(
              upcomingMissions: _upcomingMissions,
              onMissionTap: _handleUpcomingMissionTap,
              onMissionAction: _handleUpcomingMissionAction,
            ),
            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }

  Widget _buildMissionsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'assignment',
            color: AppTheme.lightTheme.colorScheme.secondary,
            size: 64,
          ),
          SizedBox(height: 2.h),
          Text(
            'Missions View',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 1.h),
          Text(
            'Detailed mission management coming soon',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.secondary,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'history',
            color: AppTheme.lightTheme.colorScheme.secondary,
            size: 64,
          ),
          SizedBox(height: 2.h),
          Text(
            'Mission History',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 1.h),
          Text(
            'View your completed missions and performance',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.secondary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'person',
            color: AppTheme.lightTheme.colorScheme.secondary,
            size: 64,
          ),
          SizedBox(height: 2.h),
          Text(
            'Driver Profile',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 1.h),
          Text(
            'Manage your profile and settings',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.secondary,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          ElevatedButton(
            onPressed: () => Navigator.pushNamed(context, '/login-screen'),
            child: Text('Logout'),
          ),
        ],
      ),
    );
  }
}
