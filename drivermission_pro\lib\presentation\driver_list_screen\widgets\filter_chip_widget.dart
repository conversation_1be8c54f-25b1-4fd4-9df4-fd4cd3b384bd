import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FilterChipWidget extends StatelessWidget {
  final String filterType;
  final int count;
  final VoidCallback onRemove;

  const FilterChipWidget({
    super.key,
    required this.filterType,
    required this.count,
    required this.onRemove,
  });

  String _getFilterLabel(String filterType) {
    switch (filterType.toLowerCase()) {
      case 'available':
        return 'Available';
      case 'unavailable':
        return 'Unavailable';
      case 'on_mission':
        return 'On Mission';
      default:
        return 'All Drivers';
    }
  }

  Color _getFilterColor(String filterType) {
    switch (filterType.toLowerCase()) {
      case 'available':
        return AppTheme.lightTheme.colorScheme.tertiary;
      case 'on_mission':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'unavailable':
        return AppTheme.lightTheme.colorScheme.error;
      default:
        return AppTheme.lightTheme.colorScheme.secondary;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (filterType == 'all') return SizedBox.shrink();

    final Color filterColor = _getFilterColor(filterType);
    final String label = _getFilterLabel(filterType);

    return Container(
      margin: EdgeInsets.only(right: 2.w),
      child: Chip(
        backgroundColor: filterColor.withValues(alpha: 0.1),
        side: BorderSide(
          color: filterColor,
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: filterColor,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            if (count > 0) ...[
              SizedBox(width: 1.w),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 1.5.w,
                  vertical: 0.2.h,
                ),
                decoration: BoxDecoration(
                  color: filterColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  count.toString(),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.surface,
                        fontWeight: FontWeight.w700,
                        fontSize: 9.sp,
                      ),
                ),
              ),
            ],
          ],
        ),
        deleteIcon: CustomIconWidget(
          iconName: 'close',
          color: filterColor,
          size: 4.w,
        ),
        onDeleted: onRemove,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }
}
