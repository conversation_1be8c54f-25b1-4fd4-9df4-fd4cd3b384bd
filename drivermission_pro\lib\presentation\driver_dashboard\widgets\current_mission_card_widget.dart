import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CurrentMissionCardWidget extends StatelessWidget {
  final Map<String, dynamic>? currentMission;
  final Function(String)? onMissionAction;

  const CurrentMissionCardWidget({
    Key? key,
    this.currentMission,
    this.onMissionAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (currentMission == null) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(6.w),
        margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            CustomIconWidget(
              iconName: 'assignment',
              color: AppTheme.lightTheme.colorScheme.secondary,
              size: 48,
            ),
            SizedBox(height: 2.h),
            Text(
              'No Active Missions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.secondary,
                  ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Stay available for new assignments',
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final String status = currentMission!['status'] as String;
    final bool isPending = status == 'pending';
    final bool isInProgress = status == 'in_progress';

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(6.w),
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Current Mission',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.5.h),
                decoration: BoxDecoration(
                  color: _getStatusColor(status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status.toUpperCase(),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: _getStatusColor(status),
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          Row(
            children: [
              CustomIconWidget(
                iconName: 'location_on',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  currentMission!['destination'] as String,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              CustomIconWidget(
                iconName: 'schedule',
                color: AppTheme.lightTheme.colorScheme.secondary,
                size: 18,
              ),
              SizedBox(width: 2.w),
              Text(
                'Est. Time: ${currentMission!['estimatedTime']}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              CustomIconWidget(
                iconName: 'directions_car',
                color: AppTheme.lightTheme.colorScheme.secondary,
                size: 18,
              ),
              SizedBox(width: 2.w),
              Text(
                'Vehicle: ${currentMission!['vehicle']}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 4.h),
          isPending
              ? _buildPendingActions()
              : isInProgress
                  ? _buildInProgressActions()
                  : _buildCompletedActions(),
        ],
      ),
    );
  }

  Widget _buildPendingActions() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => onMissionAction?.call('reject'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.lightTheme.colorScheme.error,
              side: BorderSide(color: AppTheme.lightTheme.colorScheme.error),
              padding: EdgeInsets.symmetric(vertical: 2.h),
            ),
            child: Text('Reject'),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: () => onMissionAction?.call('accept'),
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 2.h),
            ),
            child: Text('Accept Mission'),
          ),
        ),
      ],
    );
  }

  Widget _buildInProgressActions() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => onMissionAction?.call('complete'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.lightTheme.colorScheme.tertiary,
          padding: EdgeInsets.symmetric(vertical: 2.h),
        ),
        child: Text('Complete Mission'),
      ),
    );
  }

  Widget _buildCompletedActions() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 2.h),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.tertiary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'check_circle',
            color: AppTheme.lightTheme.colorScheme.tertiary,
            size: 20,
          ),
          SizedBox(width: 2.w),
          Text(
            'Mission Completed',
            style: TextStyle(
              color: AppTheme.lightTheme.colorScheme.tertiary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'in_progress':
        return Color(0xFFD97706);
      case 'completed':
        return AppTheme.lightTheme.colorScheme.tertiary;
      default:
        return AppTheme.lightTheme.colorScheme.secondary;
    }
  }
}
