import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DriverDetailModalWidget extends StatelessWidget {
  final Map<String, dynamic> driver;

  const DriverDetailModalWidget({
    super.key,
    required this.driver,
  });

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return AppTheme.lightTheme.colorScheme.tertiary;
      case 'on_mission':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'unavailable':
        return AppTheme.lightTheme.colorScheme.error;
      default:
        return AppTheme.lightTheme.colorScheme.secondary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return 'Available';
      case 'on_mission':
        return 'On Mission';
      case 'unavailable':
        return 'Unavailable';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    final String name = driver['name'] ?? 'Unknown Driver';
    final String status = driver['status'] ?? 'unknown';
    final String phone = driver['phone'] ?? 'No phone number';
    final String email = driver['email'] ?? 'No email';
    final String licenseNumber = driver['licenseNumber'] ?? 'No license';
    final String profileImage = driver['profileImage'] ?? '';
    final List<dynamic> missionHistory = driver['missionHistory'] ?? [];
    final Map<String, dynamic> stats = driver['stats'] ?? {};

    return Container(
      height: 85.h,
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle Bar
          Container(
            margin: EdgeInsets.only(top: 1.h),
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant
                  .withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Container(
            padding: EdgeInsets.all(4.w),
            child: Row(
              children: [
                // Profile Image
                Container(
                  width: 20.w,
                  height: 20.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _getStatusColor(status),
                      width: 3,
                    ),
                  ),
                  child: ClipOval(
                    child: profileImage.isNotEmpty
                        ? CustomImageWidget(
                            imageUrl: profileImage,
                            width: 20.w,
                            height: 20.w,
                            fit: BoxFit.cover,
                          )
                        : Container(
                            color: AppTheme.lightTheme.colorScheme.surface,
                            child: CustomIconWidget(
                              iconName: 'person',
                              color: AppTheme.lightTheme.colorScheme.secondary,
                              size: 10.w,
                            ),
                          ),
                  ),
                ),
                SizedBox(width: 4.w),

                // Driver Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.w700,
                                ),
                      ),
                      SizedBox(height: 1.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 3.w,
                          vertical: 1.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(status).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: _getStatusColor(status),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _getStatusText(status),
                          style:
                              Theme.of(context).textTheme.labelMedium?.copyWith(
                                    color: _getStatusColor(status),
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Close Button
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: CustomIconWidget(
                    iconName: 'close',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 6.w,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Contact Information
                  _buildSectionTitle(context, 'Contact Information'),
                  SizedBox(height: 2.h),
                  _buildInfoCard(context, [
                    {'icon': 'phone', 'label': 'Phone', 'value': phone},
                    {'icon': 'email', 'label': 'Email', 'value': email},
                    {
                      'icon': 'badge',
                      'label': 'License',
                      'value': licenseNumber
                    },
                  ]),

                  SizedBox(height: 3.h),

                  // Statistics
                  _buildSectionTitle(context, 'Performance Statistics'),
                  SizedBox(height: 2.h),
                  _buildStatsCard(context, stats),

                  SizedBox(height: 3.h),

                  // Mission History
                  _buildSectionTitle(context, 'Recent Mission History'),
                  SizedBox(height: 2.h),
                  _buildMissionHistory(context, missionHistory),

                  SizedBox(height: 4.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
    );
  }

  Widget _buildInfoCard(BuildContext context, List<Map<String, String>> items) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          children: items
              .map((item) => _buildInfoRow(
                    context,
                    item['icon']!,
                    item['label']!,
                    item['value']!,
                  ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, String icon, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: icon,
            color: AppTheme.lightTheme.colorScheme.primary,
            size: 5.w,
          ),
          SizedBox(width: 4.w),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(BuildContext context, Map<String, dynamic> stats) {
    final int totalMissions = stats['totalMissions'] ?? 0;
    final int completedMissions = stats['completedMissions'] ?? 0;
    final double rating = (stats['rating'] ?? 0.0).toDouble();
    final int onTimeDeliveries = stats['onTimeDeliveries'] ?? 0;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Missions',
                    totalMissions.toString(),
                    'assignment',
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Completed',
                    completedMissions.toString(),
                    'check_circle',
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Rating',
                    rating.toStringAsFixed(1),
                    'star',
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'On Time',
                    onTimeDeliveries.toString(),
                    'schedule',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      BuildContext context, String label, String value, String icon) {
    return Column(
      children: [
        CustomIconWidget(
          iconName: icon,
          color: AppTheme.lightTheme.colorScheme.primary,
          size: 8.w,
        ),
        SizedBox(height: 1.h),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMissionHistory(BuildContext context, List<dynamic> missions) {
    if (missions.isEmpty) {
      return Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Column(
            children: [
              CustomIconWidget(
                iconName: 'history',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 12.w,
              ),
              SizedBox(height: 2.h),
              Text(
                'No mission history available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: missions.take(5).map((mission) {
        final Map<String, dynamic> missionData =
            mission as Map<String, dynamic>;
        return Card(
          elevation: 1,
          margin: EdgeInsets.only(bottom: 1.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: ListTile(
            leading: Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: _getStatusColor(missionData['status'] ?? 'unknown')
                    .withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: CustomIconWidget(
                iconName: 'local_shipping',
                color: _getStatusColor(missionData['status'] ?? 'unknown'),
                size: 5.w,
              ),
            ),
            title: Text(
              missionData['destination'] ?? 'Unknown Destination',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            subtitle: Text(
              missionData['date'] ?? 'Unknown Date',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            trailing: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 2.w,
                vertical: 0.5.h,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor(missionData['status'] ?? 'unknown')
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(missionData['status'] ?? 'unknown'),
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color:
                          _getStatusColor(missionData['status'] ?? 'unknown'),
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
