import 'package:flutter/foundation.dart';
import '../utils/supabase_service.dart';
import '../models/driver_model.dart';

class DriverService {
  static final DriverService _instance = DriverService._internal();
  factory DriverService() => _instance;
  DriverService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Get all drivers with user profile data
  Future<List<DriverModel>> getAllDrivers() async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        // Return mock data if Supabase not configured
        return _getMockDrivers();
      }

      final response = await client.from('drivers').select('''
            *,
            user_profiles:user_id (
              id,
              full_name,
              email,
              phone
            )
          ''').order('created_at', ascending: false);

      return response
          .map<DriverModel>((json) => DriverModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to fetch drivers: $error');
      return _getMockDrivers();
    }
  }

  // Get available drivers only
  Future<List<DriverModel>> getAvailableDrivers() async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockDrivers().where((d) => d.status == 'available').toList();
      }

      final response = await client
          .from('drivers')
          .select('''
            *,
            user_profiles:user_id (
              id,
              full_name,
              email,
              phone
            )
          ''')
          .eq('status', 'available')
          .order('last_activity', ascending: false);

      return response
          .map<DriverModel>((json) => DriverModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to fetch available drivers: $error');
      return _getMockDrivers().where((d) => d.status == 'available').toList();
    }
  }

  // Update driver status
  Future<bool> updateDriverStatus(String driverId, String status,
      {String? reason}) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        debugPrint('Supabase not configured - status update simulated');
        return true;
      }

      final updateData = <String, dynamic>{
        'status': status,
        'last_activity': DateTime.now().toIso8601String(),
      };

      if (reason != null) {
        updateData['status_reason'] = reason;
      }

      await client.from('drivers').update(updateData).eq('id', driverId);

      return true;
    } catch (error) {
      debugPrint('Failed to update driver status: $error');
      return false;
    }
  }

  // Get driver by user ID
  Future<DriverModel?> getDriverByUserId(String userId) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockDrivers().first;
      }

      final response = await client.from('drivers').select('''
            *,
            user_profiles:user_id (
              id,
              full_name,
              email,
              phone
            )
          ''').eq('user_id', userId).single();

      return DriverModel.fromJson(response);
    } catch (error) {
      debugPrint('Failed to fetch driver by user ID: $error');
      return null;
    }
  }

  // Filter drivers by status
  Future<List<DriverModel>> filterDriversByStatus(String status) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockDrivers().where((d) => d.status == status).toList();
      }

      final response = await client.from('drivers').select('''
            *,
            user_profiles:user_id (
              id,
              full_name,
              email,
              phone
            )
          ''').eq('status', status).order('last_activity', ascending: false);

      return response
          .map<DriverModel>((json) => DriverModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to filter drivers: $error');
      return _getMockDrivers().where((d) => d.status == status).toList();
    }
  }

  // Search drivers by name
  Future<List<DriverModel>> searchDrivers(String query) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockDrivers()
            .where((d) =>
                d.fullName?.toLowerCase().contains(query.toLowerCase()) ??
                false)
            .toList();
      }

      final response = await client.from('drivers').select('''
            *,
            user_profiles:user_id (
              id,
              full_name,
              email,
              phone
            )
          ''').ilike('user_profiles.full_name', '%$query%');

      return response
          .map<DriverModel>((json) => DriverModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to search drivers: $error');
      return _getMockDrivers()
          .where((d) =>
              d.fullName?.toLowerCase().contains(query.toLowerCase()) ?? false)
          .toList();
    }
  }

  // Mock data fallback
  List<DriverModel> _getMockDrivers() {
    return [
      DriverModel(
        id: '1',
        userId: 'user1',
        licenseNumber: 'DL-2024-001',
        status: 'available',
        lastActivity: DateTime.now().subtract(const Duration(minutes: 2)),
        totalMissions: 156,
        completedMissions: 148,
        rating: 4.8,
        onTimeDeliveries: 142,
        fullName: 'Michael Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
      ),
      DriverModel(
        id: '2',
        userId: 'user2',
        licenseNumber: 'DL-2024-002',
        status: 'on_mission',
        lastActivity: DateTime.now().subtract(const Duration(minutes: 15)),
        totalMissions: 203,
        completedMissions: 195,
        rating: 4.9,
        onTimeDeliveries: 188,
        fullName: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+****************',
      ),
      DriverModel(
        id: '3',
        userId: 'user3',
        licenseNumber: 'DL-2024-003',
        status: 'available',
        lastActivity: DateTime.now().subtract(const Duration(minutes: 5)),
        totalMissions: 89,
        completedMissions: 84,
        rating: 4.7,
        onTimeDeliveries: 79,
        fullName: 'David Chen',
        email: '<EMAIL>',
        phone: '+****************',
      ),
      DriverModel(
        id: '4',
        userId: 'user4',
        licenseNumber: 'DL-2024-004',
        status: 'unavailable',
        lastActivity: DateTime.now().subtract(const Duration(hours: 1)),
        totalMissions: 134,
        completedMissions: 128,
        rating: 4.6,
        onTimeDeliveries: 121,
        fullName: 'Emily Davis',
        email: '<EMAIL>',
        phone: '+****************',
      ),
      DriverModel(
        id: '5',
        userId: 'user5',
        licenseNumber: 'DL-2024-005',
        status: 'available',
        lastActivity: DateTime.now().subtract(const Duration(minutes: 8)),
        totalMissions: 178,
        completedMissions: 169,
        rating: 4.8,
        onTimeDeliveries: 162,
        fullName: 'James Wilson',
        email: '<EMAIL>',
        phone: '+****************',
      ),
      DriverModel(
        id: '6',
        userId: 'user6',
        licenseNumber: 'DL-2024-006',
        status: 'on_mission',
        lastActivity: DateTime.now().subtract(const Duration(minutes: 22)),
        totalMissions: 145,
        completedMissions: 138,
        rating: 4.7,
        onTimeDeliveries: 131,
        fullName: 'Lisa Thompson',
        email: '<EMAIL>',
        phone: '+****************',
      ),
    ];
  }
}
