import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EmptyStateWidget extends StatelessWidget {
  final String filterType;
  final VoidCallback? onClearFilter;
  final VoidCallback? onRefresh;

  const EmptyStateWidget({
    super.key,
    required this.filterType,
    this.onClearFilter,
    this.onRefresh,
  });

  Map<String, dynamic> _getEmptyStateData(String filterType) {
    switch (filterType.toLowerCase()) {
      case 'available':
        return {
          'icon': 'person_off',
          'title': 'No Available Drivers',
          'message':
              'All drivers are currently busy or unavailable. Check back in a few minutes or try adjusting your filters.',
          'actionText': 'Clear Filter',
        };
      case 'unavailable':
        return {
          'icon': 'cancel',
          'title': 'No Unavailable Drivers',
          'message':
              'Great! All drivers are currently available for mission assignments.',
          'actionText': 'View All Drivers',
        };
      case 'on_mission':
        return {
          'icon': 'local_shipping',
          'title': 'No Active Missions',
          'message':
              'No drivers are currently on missions. All drivers are available for new assignments.',
          'actionText': 'View All Drivers',
        };
      default:
        return {
          'icon': 'people_outline',
          'title': 'No Drivers Found',
          'message':
              'No drivers match your current search criteria. Try adjusting your search or filters.',
          'actionText': 'Clear Filters',
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    final emptyStateData = _getEmptyStateData(filterType);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty State Icon
            Container(
              width: 30.w,
              height: 30.w,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primary
                    .withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: emptyStateData['icon'],
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 15.w,
                ),
              ),
            ),

            SizedBox(height: 4.h),

            // Title
            Text(
              emptyStateData['title'],
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 2.h),

            // Message
            Text(
              emptyStateData['message'],
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 4.h),

            // Action Buttons
            Column(
              children: [
                if (onClearFilter != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: onClearFilter,
                      icon: CustomIconWidget(
                        iconName: 'clear_all',
                        color: AppTheme.lightTheme.colorScheme.onPrimary,
                        size: 5.w,
                      ),
                      label: Text(emptyStateData['actionText']),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 2.h),
                      ),
                    ),
                  ),
                if (onRefresh != null) ...[
                  if (onClearFilter != null) SizedBox(height: 2.h),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: onRefresh,
                      icon: CustomIconWidget(
                        iconName: 'refresh',
                        color: AppTheme.lightTheme.colorScheme.primary,
                        size: 5.w,
                      ),
                      label: Text('Refresh'),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 2.h),
                      ),
                    ),
                  ),
                ],
              ],
            ),

            SizedBox(height: 2.h),

            // Additional Help Text
            if (filterType == 'available')
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.tertiary
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.lightTheme.colorScheme.tertiary
                        .withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'info',
                      color: AppTheme.lightTheme.colorScheme.tertiary,
                      size: 5.w,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Text(
                        'Estimated availability: 15-30 minutes based on current missions',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.tertiary,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
