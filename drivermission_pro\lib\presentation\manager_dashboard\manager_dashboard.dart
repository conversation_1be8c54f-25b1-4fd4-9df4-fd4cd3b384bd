import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/active_missions_widget.dart';
import './widgets/dashboard_header_widget.dart';
import './widgets/driver_status_overview_widget.dart';
import './widgets/recent_activity_widget.dart';

class ManagerDashboard extends StatefulWidget {
  const ManagerDashboard({super.key});

  @override
  State<ManagerDashboard> createState() => _ManagerDashboardState();
}

class _ManagerDashboardState extends State<ManagerDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isRefreshing = false;

  // Mock data
  final String managerName = "<PERSON>";
  final int notificationCount = 3;
  final int availableDrivers = 12;
  final int unavailableDrivers = 4;
  final int activeMissionsCount = 8;
  final int urgentMissionsCount = 2;

  final List<Map<String, dynamic>> recentActivities = [
    {
      "id": 1,
      "type": "mission_assigned",
      "driverName": "<PERSON>",
      "description": "Assigned delivery mission to Downtown Plaza",
      "timestamp": DateTime.now().subtract(const Duration(minutes: 15)),
      "status": "pending",
    },
    {
      "id": 2,
      "type": "mission_completed",
      "driverName": "Jennifer Chen",
      "description": "Completed pickup from Warehouse District",
      "timestamp": DateTime.now().subtract(const Duration(hours: 1)),
      "status": "completed",
    },
    {
      "id": 3,
      "type": "driver_status_changed",
      "driverName": "David Thompson",
      "description": "Changed status to unavailable - Vehicle maintenance",
      "timestamp": DateTime.now().subtract(const Duration(hours: 2)),
      "status": "unavailable",
    },
    {
      "id": 4,
      "type": "mission_rejected",
      "driverName": "Lisa Park",
      "description": "Rejected mission due to scheduling conflict",
      "timestamp": DateTime.now().subtract(const Duration(hours: 3)),
      "status": "rejected",
    },
    {
      "id": 5,
      "type": "mission_assigned",
      "driverName": "Robert Wilson",
      "description": "Assigned urgent delivery to Airport Terminal",
      "timestamp": DateTime.now().subtract(const Duration(hours: 4)),
      "status": "in_progress",
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isRefreshing = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Dashboard data refreshed'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _handleNotificationTap() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notifications'),
        content: Text('You have $notificationCount new notifications'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _handleDriverStatusTap() {
    Navigator.pushNamed(context, '/driver-list-screen');
  }

  void _handleViewAllMissions() {
    Navigator.pushNamed(context, '/mission-detail-screen');
  }

  void _handleCreateMission() {
    Navigator.pushNamed(context, '/create-mission-screen');
  }

  void _handleActivityLongPress(Map<String, dynamic> activity) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Activity Options',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'visibility',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: const Text('View Details'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/mission-detail-screen');
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'phone',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: const Text('Contact Driver'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Calling ${activity['driverName']}...'),
                  ),
                );
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'edit',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: const Text('Update Status'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Status update feature coming soon'),
                  ),
                );
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Tab Bar
            Container(
              color: AppTheme.lightTheme.colorScheme.surface,
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Dashboard'),
                  Tab(text: 'Drivers'),
                  Tab(text: 'Missions'),
                  Tab(text: 'Profile'),
                ],
              ),
            ),
            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Dashboard Tab (Active)
                  _buildDashboardContent(),
                  // Other tabs - placeholder content
                  _buildPlaceholderTab('Drivers'),
                  _buildPlaceholderTab('Missions'),
                  _buildPlaceholderTab('Profile'),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton.extended(
              onPressed: _handleCreateMission,
              icon: CustomIconWidget(
                iconName: 'add',
                color: AppTheme.lightTheme.colorScheme.onPrimary,
                size: 24,
              ),
              label: Text(
                'Create Mission',
                style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onPrimary,
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDashboardContent() {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // Header
            DashboardHeaderWidget(
              managerName: managerName,
              notificationCount: notificationCount,
              onNotificationTap: _handleNotificationTap,
            ),
            SizedBox(height: 1.h),
            // Driver Status Overview
            DriverStatusOverviewWidget(
              availableDrivers: availableDrivers,
              unavailableDrivers: unavailableDrivers,
              onTap: _handleDriverStatusTap,
            ),
            // Active Missions
            ActiveMissionsWidget(
              activeMissionsCount: activeMissionsCount,
              urgentMissionsCount: urgentMissionsCount,
              onViewAll: _handleViewAllMissions,
            ),
            // Recent Activity
            RecentActivityWidget(
              activities: recentActivities,
              onActivityLongPress: _handleActivityLongPress,
            ),
            SizedBox(height: 10.h), // Space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderTab(String tabName) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'construction',
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 64,
          ),
          SizedBox(height: 2.h),
          Text(
            '$tabName Tab',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Coming Soon',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
