import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class DriverSelectionWidget extends StatefulWidget {
  final Map<String, dynamic>? selectedDriver;
  final Function(Map<String, dynamic>) onDriverSelected;

  const DriverSelectionWidget({
    super.key,
    this.selectedDriver,
    required this.onDriverSelected,
  });

  @override
  State<DriverSelectionWidget> createState() => _DriverSelectionWidgetState();
}

class _DriverSelectionWidgetState extends State<DriverSelectionWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredDrivers = [];
  bool _isDropdownOpen = false;

  final List<Map<String, dynamic>> _availableDrivers = [
    {
      "id": 1,
      "name": "<PERSON>",
      "avatar":
          "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400",
      "status": "available",
      "lastSeen": "2 minutes ago",
      "rating": 4.8,
      "completedMissions": 156,
    },
    {
      "id": 2,
      "name": "<PERSON>",
      "avatar":
          "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400",
      "status": "available",
      "lastSeen": "5 minutes ago",
      "rating": 4.9,
      "completedMissions": 203,
    },
    {
      "id": 3,
      "name": "David Chen",
      "avatar":
          "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400",
      "status": "available",
      "lastSeen": "1 minute ago",
      "rating": 4.7,
      "completedMissions": 89,
    },
    {
      "id": 4,
      "name": "Emma Wilson",
      "avatar":
          "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400",
      "status": "available",
      "lastSeen": "3 minutes ago",
      "rating": 4.6,
      "completedMissions": 134,
    },
  ];

  @override
  void initState() {
    super.initState();
    _filteredDrivers = _availableDrivers;
    _searchController.addListener(_filterDrivers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterDrivers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredDrivers = _availableDrivers.where((driver) {
        final name = (driver["name"] as String).toLowerCase();
        return name.contains(query);
      }).toList();
    });
  }

  void _toggleDropdown() {
    setState(() {
      _isDropdownOpen = !_isDropdownOpen;
    });
  }

  void _selectDriver(Map<String, dynamic> driver) {
    widget.onDriverSelected(driver);
    setState(() {
      _isDropdownOpen = false;
    });
    _searchController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'person',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Select Driver',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  ' *',
                  style: TextStyle(
                    color: AppTheme.lightTheme.colorScheme.error,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            GestureDetector(
              onTap: _toggleDropdown,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppTheme.lightTheme.colorScheme.outline,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                  color: AppTheme.lightTheme.colorScheme.surface,
                ),
                child: Row(
                  children: [
                    widget.selectedDriver != null
                        ? Expanded(
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: CustomImageWidget(
                                    imageUrl: widget.selectedDriver!["avatar"]
                                        as String,
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                SizedBox(width: 3.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        widget.selectedDriver!["name"]
                                            as String,
                                        style: AppTheme
                                            .lightTheme.textTheme.bodyMedium
                                            ?.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        'Available • ${widget.selectedDriver!["lastSeen"]}',
                                        style: AppTheme
                                            .lightTheme.textTheme.bodySmall
                                            ?.copyWith(
                                          color: AppTheme
                                              .lightTheme.colorScheme.tertiary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Expanded(
                            child: Text(
                              'Choose an available driver',
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                color: AppTheme
                                    .lightTheme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                    CustomIconWidget(
                      iconName: _isDropdownOpen
                          ? 'keyboard_arrow_up'
                          : 'keyboard_arrow_down',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
            _isDropdownOpen ? SizedBox(height: 1.h) : const SizedBox.shrink(),
            _isDropdownOpen
                ? Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppTheme.lightTheme.colorScheme.outline,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(8.0),
                      color: AppTheme.lightTheme.colorScheme.surface,
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.all(3.w),
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search drivers...',
                              prefixIcon: Padding(
                                padding: EdgeInsets.all(3.w),
                                child: CustomIconWidget(
                                  iconName: 'search',
                                  color: AppTheme
                                      .lightTheme.colorScheme.onSurfaceVariant,
                                  size: 20,
                                ),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                                borderSide: BorderSide(
                                  color:
                                      AppTheme.lightTheme.colorScheme.outline,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 4.w,
                                vertical: 1.5.h,
                              ),
                            ),
                          ),
                        ),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxHeight: 30.h),
                          child: ListView.separated(
                            shrinkWrap: true,
                            itemCount: _filteredDrivers.length,
                            separatorBuilder: (context, index) => Divider(
                              height: 1,
                              color: AppTheme.lightTheme.colorScheme.outline,
                            ),
                            itemBuilder: (context, index) {
                              final driver = _filteredDrivers[index];
                              return ListTile(
                                onTap: () => _selectDriver(driver),
                                leading: ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: CustomImageWidget(
                                    imageUrl: driver["avatar"] as String,
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                title: Text(
                                  driver["name"] as String,
                                  style: AppTheme
                                      .lightTheme.textTheme.bodyMedium
                                      ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          width: 8,
                                          height: 8,
                                          decoration: BoxDecoration(
                                            color: AppTheme.lightTheme
                                                .colorScheme.tertiary,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        SizedBox(width: 1.w),
                                        Text(
                                          'Available • ${driver["lastSeen"]}',
                                          style: AppTheme
                                              .lightTheme.textTheme.bodySmall
                                              ?.copyWith(
                                            color: AppTheme.lightTheme
                                                .colorScheme.tertiary,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 0.5.h),
                                    Row(
                                      children: [
                                        CustomIconWidget(
                                          iconName: 'star',
                                          color: Colors.amber,
                                          size: 14,
                                        ),
                                        SizedBox(width: 1.w),
                                        Text(
                                          '${driver["rating"]} • ${driver["completedMissions"]} missions',
                                          style: AppTheme
                                              .lightTheme.textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                trailing: CustomIconWidget(
                                  iconName: 'check_circle_outline',
                                  color: AppTheme
                                      .lightTheme.colorScheme.onSurfaceVariant,
                                  size: 20,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
