class MissionModel {
  final String id;
  final String missionObject;
  final String destination;
  final DateTime scheduledDate;
  final String? driverId;
  final String? carId;
  final String? createdBy;
  final String status;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? acceptedAt;
  final DateTime? completedAt;

  // Related data
  final String? driverName;
  final String? carModel;
  final String? licensePlate;
  final String? managerName;

  const MissionModel({
    required this.id,
    required this.missionObject,
    required this.destination,
    required this.scheduledDate,
    this.driverId,
    this.carId,
    this.createdBy,
    required this.status,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.acceptedAt,
    this.completedAt,
    this.driverName,
    this.carModel,
    this.licensePlate,
    this.managerName,
  });

  factory MissionModel.fromJson(Map<String, dynamic> json) {
    final driver = json['drivers'];
    final car = json['cars'];
    final manager = json['user_profiles'];

    return MissionModel(
      id: json['id'] as String,
      missionObject: json['mission_object'] as String,
      destination: json['destination'] as String,
      scheduledDate: DateTime.parse(json['scheduled_date'] as String),
      driverId: json['driver_id'] as String?,
      carId: json['car_id'] as String?,
      createdBy: json['created_by'] as String?,
      status: json['status'] as String,
      notes: json['notes'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      acceptedAt: json['accepted_at'] != null
          ? DateTime.parse(json['accepted_at'] as String)
          : null,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      driverName: driver?['user_profiles']?['full_name'] as String?,
      carModel: car?['car_model'] as String?,
      licensePlate: car?['license_plate'] as String?,
      managerName: manager?['full_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mission_object': missionObject,
      'destination': destination,
      'scheduled_date': scheduledDate.toIso8601String(),
      'driver_id': driverId,
      'car_id': carId,
      'created_by': createdBy,
      'status': status,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'accepted_at': acceptedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
    };
  }

  // Status display helpers
  String get displayStatus {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  String get vehicleInfo {
    if (carModel != null && licensePlate != null) {
      return '$carModel ($licensePlate)';
    }
    return carModel ?? licensePlate ?? 'Unknown Vehicle';
  }

  bool get isActive => ['pending', 'accepted', 'in_progress'].contains(status);
  bool get isCompleted => status == 'completed';
  bool get isPending => status == 'pending';
  bool get isInProgress => status == 'in_progress';

  String get timeUntilScheduled {
    final difference = scheduledDate.difference(DateTime.now());

    if (difference.isNegative) {
      final pastDifference = DateTime.now().difference(scheduledDate);
      if (pastDifference.inDays > 0) {
        return '${pastDifference.inDays} days overdue';
      } else if (pastDifference.inHours > 0) {
        return '${pastDifference.inHours} hours overdue';
      } else {
        return '${pastDifference.inMinutes} minutes overdue';
      }
    } else {
      if (difference.inDays > 0) {
        return 'In ${difference.inDays} days';
      } else if (difference.inHours > 0) {
        return 'In ${difference.inHours} hours';
      } else {
        return 'In ${difference.inMinutes} minutes';
      }
    }
  }

  MissionModel copyWith({
    String? id,
    String? missionObject,
    String? destination,
    DateTime? scheduledDate,
    String? driverId,
    String? carId,
    String? createdBy,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? acceptedAt,
    DateTime? completedAt,
    String? driverName,
    String? carModel,
    String? licensePlate,
    String? managerName,
  }) {
    return MissionModel(
      id: id ?? this.id,
      missionObject: missionObject ?? this.missionObject,
      destination: destination ?? this.destination,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      driverId: driverId ?? this.driverId,
      carId: carId ?? this.carId,
      createdBy: createdBy ?? this.createdBy,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      completedAt: completedAt ?? this.completedAt,
      driverName: driverName ?? this.driverName,
      carModel: carModel ?? this.carModel,
      licensePlate: licensePlate ?? this.licensePlate,
      managerName: managerName ?? this.managerName,
    );
  }
}
