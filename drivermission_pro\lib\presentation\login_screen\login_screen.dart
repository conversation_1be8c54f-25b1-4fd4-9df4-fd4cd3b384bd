import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../utils/supabase_service.dart';
import './widgets/app_logo_widget.dart';
import './widgets/biometric_prompt_widget.dart';
import './widgets/login_button_widget.dart';
import './widgets/login_form_widget.dart';
import './widgets/role_selector_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();

  final SupabaseService _supabaseService = SupabaseService();

  String _selectedRole = 'Manager';
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _showBiometricPrompt = false;
  String? _emailError;
  String? _passwordError;
  String? _generalError;

  // Mock credentials for different user types (fallback when Supabase not configured)
  final Map<String, Map<String, String>> _mockCredentials = {
    '<EMAIL>': {
      'password': 'Manager123!',
      'role': 'Manager',
    },
    '<EMAIL>': {
      'password': 'Driver123!',
      'role': 'Driver',
    },
    '<EMAIL>': {
      'password': 'Admin123!',
      'role': 'Manager',
    },
  };

  @override
  void initState() {
    super.initState();
    _loadSavedEmail();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _loadSavedEmail() {
    // Load default email based on role
    _emailController.text = '<EMAIL>';
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  bool get _isFormValid {
    return _isValidEmail(_emailController.text) &&
        _isValidPassword(_passwordController.text);
  }

  void _validateInputs() {
    setState(() {
      _emailError = null;
      _passwordError = null;
      _generalError = null;

      if (_emailController.text.isEmpty) {
        _emailError = 'Email is required';
      } else if (!_isValidEmail(_emailController.text)) {
        _emailError = 'Please enter a valid email address';
      }

      if (_passwordController.text.isEmpty) {
        _passwordError = 'Password is required';
      } else if (!_isValidPassword(_passwordController.text)) {
        _passwordError = 'Password must be at least 6 characters';
      }
    });
  }

  Future<void> _handleLogin() async {
    _validateInputs();

    if (!_isFormValid) return;

    setState(() {
      _isLoading = true;
      _generalError = null;
    });

    try {
      final email = _emailController.text.toLowerCase();
      final password = _passwordController.text;

      // Try Supabase authentication first
      if (_supabaseService.isConfigured) {
        final response = await _supabaseService.signIn(email, password);

        if (response?.user != null) {
          // Successful Supabase authentication
          HapticFeedback.lightImpact();
          _navigateToRoleDashboard();
          return;
        }
      }

      // Fallback to mock authentication if Supabase fails or not configured
      await Future.delayed(
          const Duration(seconds: 1)); // Simulate network delay

      if (_mockCredentials.containsKey(email)) {
        final credentials = _mockCredentials[email]!;
        if (credentials['password'] == password) {
          // Successful mock authentication
          HapticFeedback.lightImpact();

          // Show preview mode banner
          if (!_supabaseService.isConfigured) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Running in Preview Mode - Configure Supabase for full functionality'),
                backgroundColor: AppTheme.lightTheme.colorScheme.tertiary,
                duration: Duration(seconds: 3),
              ),
            );
          }

          // Show biometric prompt for first-time login
          if (!_showBiometricPrompt) {
            setState(() {
              _showBiometricPrompt = true;
              _isLoading = false;
            });
            return;
          }

          _navigateToRoleDashboard();
        } else {
          setState(() {
            _generalError =
                'Invalid credentials. Please check your email and password.';
          });
        }
      } else {
        setState(() {
          _generalError = 'Account not found. Please check your email address.';
        });
      }
    } catch (e) {
      setState(() {
        _generalError = 'Authentication failed. Please try again.';
      });
      debugPrint('Login error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToRoleDashboard() {
    if (_selectedRole == 'Manager') {
      Navigator.pushReplacementNamed(context, '/manager-dashboard');
    } else {
      Navigator.pushReplacementNamed(context, '/driver-dashboard');
    }
  }

  void _handleBiometricLogin() {
    // Simulate biometric authentication success
    HapticFeedback.mediumImpact();
    _navigateToRoleDashboard();
  }

  void _handleSkipBiometric() {
    setState(() {
      _showBiometricPrompt = false;
    });
    _navigateToRoleDashboard();
  }

  void _handleForgotPassword() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Forgot Password',
          style: AppTheme.lightTheme.textTheme.titleLarge,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Demo Credentials:',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text('Manager: <EMAIL> / Manager123!'),
            Text('Driver: <EMAIL> / Driver123!'),
            SizedBox(height: 1.h),
            if (!_supabaseService.isConfigured) ...[
              Text(
                'Configure Supabase for full authentication features.',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.tertiary,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 6.w),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 8.h),

                  // App Logo
                  const AppLogoWidget(),

                  SizedBox(height: 6.h),

                  // Welcome Text
                  Text(
                    'Welcome Back',
                    style:
                        AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 24.sp,
                    ),
                  ),

                  SizedBox(height: 1.h),

                  Text(
                    'Sign in to continue to DriverMission Pro',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      fontSize: 14.sp,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  // Preview mode indicator
                  if (!_supabaseService.isConfigured) ...[
                    SizedBox(height: 2.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.tertiary
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppTheme.lightTheme.colorScheme.tertiary
                              .withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomIconWidget(
                            iconName: 'preview',
                            color: AppTheme.lightTheme.colorScheme.tertiary,
                            size: 4.w,
                          ),
                          SizedBox(width: 2.w),
                          Text(
                            'Preview Mode',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.tertiary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  SizedBox(height: 5.h),

                  // Role Selector
                  RoleSelectorWidget(
                    selectedRole: _selectedRole,
                    onRoleChanged: (role) {
                      setState(() {
                        _selectedRole = role;
                        // Update email based on role selection
                        if (role == 'Manager') {
                          _emailController.text = '<EMAIL>';
                        } else {
                          _emailController.text = '<EMAIL>';
                        }
                      });
                    },
                  ),

                  SizedBox(height: 4.h),

                  // Login Form
                  LoginFormWidget(
                    emailController: _emailController,
                    passwordController: _passwordController,
                    isPasswordVisible: _isPasswordVisible,
                    onTogglePasswordVisibility: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                    onForgotPassword: _handleForgotPassword,
                    emailError: _emailError,
                    passwordError: _passwordError,
                  ),

                  SizedBox(height: 4.h),

                  // General Error Message
                  if (_generalError != null) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(3.w),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.error
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppTheme.lightTheme.colorScheme.error
                              .withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'error_outline',
                            color: AppTheme.lightTheme.colorScheme.error,
                            size: 5.w,
                          ),
                          SizedBox(width: 2.w),
                          Expanded(
                            child: Text(
                              _generalError!,
                              style: AppTheme.lightTheme.textTheme.bodySmall
                                  ?.copyWith(
                                color: AppTheme.lightTheme.colorScheme.error,
                                fontSize: 12.sp,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 3.h),
                  ],

                  // Login Button
                  LoginButtonWidget(
                    isLoading: _isLoading,
                    isEnabled: _isFormValid,
                    onPressed: _handleLogin,
                  ),

                  // Biometric Prompt
                  BiometricPromptWidget(
                    isVisible: _showBiometricPrompt,
                    onBiometricLogin: _handleBiometricLogin,
                    onSkip: _handleSkipBiometric,
                  ),

                  SizedBox(height: 8.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
