import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class DestinationInputWidget extends StatefulWidget {
  final String? destination;
  final Function(String) onDestinationChanged;

  const DestinationInputWidget({
    super.key,
    this.destination,
    required this.onDestinationChanged,
  });

  @override
  State<DestinationInputWidget> createState() => _DestinationInputWidgetState();
}

class _DestinationInputWidgetState extends State<DestinationInputWidget> {
  final TextEditingController _destinationController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;

  final List<Map<String, dynamic>> _suggestions = [
    {
      "address": "123 Main Street, Downtown",
      "city": "New York, NY",
      "distance": "2.5 km",
      "type": "business",
    },
    {
      "address": "456 Oak Avenue, Midtown",
      "city": "New York, NY",
      "distance": "3.8 km",
      "type": "residential",
    },
    {
      "address": "789 Pine Road, Uptown",
      "city": "New York, NY",
      "distance": "5.2 km",
      "type": "commercial",
    },
    {
      "address": "321 Elm Street, Brooklyn",
      "city": "New York, NY",
      "distance": "7.1 km",
      "type": "residential",
    },
  ];

  @override
  void initState() {
    super.initState();
    if (widget.destination != null) {
      _destinationController.text = widget.destination!;
    }
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _destinationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _showSuggestions =
          _focusNode.hasFocus && _destinationController.text.isNotEmpty;
    });
  }

  void _onTextChanged(String value) {
    widget.onDestinationChanged(value);
    setState(() {
      _showSuggestions = value.isNotEmpty && _focusNode.hasFocus;
    });
  }

  void _selectSuggestion(String address) {
    _destinationController.text = address;
    widget.onDestinationChanged(address);
    setState(() {
      _showSuggestions = false;
    });
    _focusNode.unfocus();
  }

  void _useCurrentLocation() {
    const currentLocation = "Current Location (GPS)";
    _destinationController.text = currentLocation;
    widget.onDestinationChanged(currentLocation);
    setState(() {
      _showSuggestions = false;
    });
    _focusNode.unfocus();
  }

  IconData _getLocationTypeIcon(String type) {
    switch (type) {
      case 'business':
        return Icons.business;
      case 'residential':
        return Icons.home;
      case 'commercial':
        return Icons.store;
      default:
        return Icons.location_on;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'location_on',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Destination',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  ' *',
                  style: TextStyle(
                    color: AppTheme.lightTheme.colorScheme.error,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            TextField(
              controller: _destinationController,
              focusNode: _focusNode,
              onChanged: _onTextChanged,
              decoration: InputDecoration(
                hintText: 'Enter destination address',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(3.w),
                  child: CustomIconWidget(
                    iconName: 'search',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
                suffixIcon: Padding(
                  padding: EdgeInsets.all(2.w),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      GestureDetector(
                        onTap: _useCurrentLocation,
                        child: Container(
                          padding: EdgeInsets.all(2.w),
                          decoration: BoxDecoration(
                            color: AppTheme.lightTheme.colorScheme.primary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: CustomIconWidget(
                            iconName: 'my_location',
                            color: AppTheme.lightTheme.colorScheme.primary,
                            size: 18,
                          ),
                        ),
                      ),
                      SizedBox(width: 2.w),
                      GestureDetector(
                        onTap: () {
                          // Map integration would go here
                        },
                        child: Container(
                          padding: EdgeInsets.all(2.w),
                          decoration: BoxDecoration(
                            color: AppTheme.lightTheme.colorScheme.secondary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: CustomIconWidget(
                            iconName: 'map',
                            color: AppTheme.lightTheme.colorScheme.secondary,
                            size: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide(
                    color: AppTheme.lightTheme.colorScheme.outline,
                  ),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 4.w,
                  vertical: 2.h,
                ),
              ),
            ),
            _showSuggestions ? SizedBox(height: 1.h) : const SizedBox.shrink(),
            _showSuggestions
                ? Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppTheme.lightTheme.colorScheme.outline,
                        width: 1.0,
                      ),
                      borderRadius: BorderRadius.circular(8.0),
                      color: AppTheme.lightTheme.colorScheme.surface,
                    ),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(maxHeight: 25.h),
                      child: ListView.separated(
                        shrinkWrap: true,
                        itemCount: _suggestions.length,
                        separatorBuilder: (context, index) => Divider(
                          height: 1,
                          color: AppTheme.lightTheme.colorScheme.outline,
                        ),
                        itemBuilder: (context, index) {
                          final suggestion = _suggestions[index];
                          return ListTile(
                            onTap: () => _selectSuggestion(
                              '${suggestion["address"]}, ${suggestion["city"]}',
                            ),
                            leading: Container(
                              padding: EdgeInsets.all(2.w),
                              decoration: BoxDecoration(
                                color: AppTheme.lightTheme.colorScheme.primary
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: CustomIconWidget(
                                iconName: _getLocationTypeIcon(
                                        suggestion["type"] as String)
                                    .codePoint
                                    .toString(),
                                color: AppTheme.lightTheme.colorScheme.primary,
                                size: 20,
                              ),
                            ),
                            title: Text(
                              suggestion["address"] as String,
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    suggestion["city"] as String,
                                    style:
                                        AppTheme.lightTheme.textTheme.bodySmall,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Text(
                                  suggestion["distance"] as String,
                                  style: AppTheme.lightTheme.textTheme.bodySmall
                                      ?.copyWith(
                                    color:
                                        AppTheme.lightTheme.colorScheme.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            trailing: CustomIconWidget(
                              iconName: 'arrow_forward_ios',
                              color: AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                              size: 16,
                            ),
                          );
                        },
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
            SizedBox(height: 2.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _useCurrentLocation,
                    icon: CustomIconWidget(
                      iconName: 'my_location',
                      color: AppTheme.lightTheme.colorScheme.primary,
                      size: 18,
                    ),
                    label: Text(
                      'Use Current Location',
                      style: AppTheme.lightTheme.textTheme.labelMedium,
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                          horizontal: 4.w, vertical: 1.5.h),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
