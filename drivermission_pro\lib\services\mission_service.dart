import 'package:flutter/foundation.dart';
import '../utils/supabase_service.dart';
import '../models/mission_model.dart';

class MissionService {
  static final MissionService _instance = MissionService._internal();
  factory MissionService() => _instance;
  MissionService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Create new mission
  Future<bool> createMission({
    required String missionObject,
    required String destination,
    required DateTime scheduledDate,
    required String driverId,
    required String carId,
    String? notes,
  }) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        debugPrint('Mission created successfully (Preview Mode)');
        return true;
      }

      final currentUser = _supabaseService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await client.from('missions').insert({
        'mission_object': missionObject,
        'destination': destination,
        'scheduled_date': scheduledDate.toIso8601String(),
        'driver_id': driverId,
        'car_id': carId,
        'created_by': currentUser.id,
        'status': 'pending',
        'notes': notes,
      });

      debugPrint('Mission created successfully');
      return true;
    } catch (error) {
      debugPrint('Failed to create mission: $error');
      rethrow;
    }
  }

  // Get missions for driver
  Future<List<MissionModel>> getMissionsForDriver(String driverId) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockMissions().where((m) => m.driverId == driverId).toList();
      }

      final response = await client
          .from('missions')
          .select('''
            *,
            cars:car_id (
              id,
              car_model,
              license_plate
            ),
            user_profiles:created_by (
              id,
              full_name,
              email
            )
          ''')
          .eq('driver_id', driverId)
          .order('scheduled_date', ascending: false);

      return response
          .map<MissionModel>((json) => MissionModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to fetch driver missions: $error');
      return _getMockMissions().where((m) => m.driverId == driverId).toList();
    }
  }

  // Get all missions (for managers)
  Future<List<MissionModel>> getAllMissions() async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockMissions();
      }

      final response = await client.from('missions').select('''
            *,
            drivers:driver_id (
              id,
              license_number,
              user_profiles:user_id (
                full_name
              )
            ),
            cars:car_id (
              id,
              car_model,
              license_plate
            ),
            user_profiles:created_by (
              id,
              full_name,
              email
            )
          ''').order('scheduled_date', ascending: false);

      return response
          .map<MissionModel>((json) => MissionModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to fetch all missions: $error');
      return _getMockMissions();
    }
  }

  // Update mission status
  Future<bool> updateMissionStatus(String missionId, String status) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        debugPrint('Mission status updated (Preview Mode)');
        return true;
      }

      final updateData = <String, dynamic>{
        'status': status,
      };

      // Add timestamps for specific statuses
      if (status == 'accepted') {
        updateData['accepted_at'] = DateTime.now().toIso8601String();
      } else if (status == 'completed') {
        updateData['completed_at'] = DateTime.now().toIso8601String();
      }

      await client.from('missions').update(updateData).eq('id', missionId);

      debugPrint('Mission status updated successfully');
      return true;
    } catch (error) {
      debugPrint('Failed to update mission status: $error');
      return false;
    }
  }

  // Get current active mission for driver
  Future<MissionModel?> getCurrentMissionForDriver(String driverId) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        final mockMissions = _getMockMissions()
            .where((m) =>
                m.driverId == driverId &&
                ['accepted', 'in_progress'].contains(m.status))
            .toList();
        return mockMissions.isNotEmpty ? mockMissions.first : null;
      }

      final response = await client
          .from('missions')
          .select('''
            *,
            cars:car_id (
              id,
              car_model,
              license_plate
            ),
            user_profiles:created_by (
              id,
              full_name,
              email
            )
          ''')
          .eq('driver_id', driverId)
          .inFilter('status', ['accepted', 'in_progress'])
          .order('scheduled_date')
          .limit(1);

      if (response.isEmpty) return null;
      return MissionModel.fromJson(response.first);
    } catch (error) {
      debugPrint('Failed to fetch current mission: $error');
      return null;
    }
  }

  // Get pending missions for driver
  Future<List<MissionModel>> getPendingMissionsForDriver(
      String driverId) async {
    try {
      final client = await _supabaseService.client;
      if (client == null) {
        return _getMockMissions()
            .where((m) => m.driverId == driverId && m.status == 'pending')
            .toList();
      }

      final response = await client
          .from('missions')
          .select('''
            *,
            cars:car_id (
              id,
              car_model,
              license_plate
            ),
            user_profiles:created_by (
              id,
              full_name,
              email
            )
          ''')
          .eq('driver_id', driverId)
          .eq('status', 'pending')
          .order('scheduled_date');

      return response
          .map<MissionModel>((json) => MissionModel.fromJson(json))
          .toList();
    } catch (error) {
      debugPrint('Failed to fetch pending missions: $error');
      return _getMockMissions()
          .where((m) => m.driverId == driverId && m.status == 'pending')
          .toList();
    }
  }

  // Mock missions for fallback
  List<MissionModel> _getMockMissions() {
    return [
      MissionModel(
        id: '1',
        missionObject: 'Deliver documents to client office',
        destination: 'Downtown Office Complex',
        scheduledDate: DateTime.now().add(const Duration(hours: 2)),
        driverId: '1',
        carId: '1',
        createdBy: 'manager1',
        status: 'pending',
        carModel: 'Toyota Camry',
        licensePlate: 'ABC-123',
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      MissionModel(
        id: '2',
        missionObject: 'Transport equipment to warehouse',
        destination: 'Industrial District',
        scheduledDate: DateTime.now().add(const Duration(days: 1)),
        driverId: '2',
        carId: '3',
        createdBy: 'manager1',
        status: 'in_progress',
        carModel: 'Ford Transit',
        licensePlate: 'GHI-789',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      MissionModel(
        id: '3',
        missionObject: 'Pick up supplies from vendor',
        destination: 'Shopping Mall Plaza',
        scheduledDate: DateTime.now().add(const Duration(hours: 3)),
        driverId: '3',
        carId: '2',
        createdBy: 'manager1',
        status: 'accepted',
        carModel: 'Honda Accord',
        licensePlate: 'DEF-456',
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
    ];
  }
}
