import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  late final SupabaseClient _client;
  bool _isInitialized = false;
  final Future<void> _initFuture;

  // Singleton pattern
  factory SupabaseService() {
    return _instance;
  }

  SupabaseService._internal() : _initFuture = _initializeSupabase();

  static const String supabaseUrl =
      String.fromEnvironment('SUPABASE_URL', defaultValue: 'your_supabase_url');
  static const String supabaseAnonKey = String.fromEnvironment(
      'SUPABASE_ANON_KEY',
      defaultValue: 'your_supabase_anon_key');

  // Internal initialization logic
  static Future<void> _initializeSupabase() async {
    try {
      if (supabaseUrl.isEmpty ||
          supabaseUrl == 'your_supabase_url' ||
          supabaseAnonKey.isEmpty ||
          supabaseAnonKey == 'your_supabase_anon_key') {
        debugPrint(
            '⚠️ Supabase credentials not configured. Using preview mode.');
        return;
      }

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
      );

      _instance._client = Supabase.instance.client;
      _instance._isInitialized = true;
      debugPrint('✅ Supabase initialized successfully');
    } catch (e) {
      debugPrint('❌ Supabase initialization failed: $e');
      // Don't throw error to prevent app crash
    }
  }

  // Client getter (async)
  Future<SupabaseClient?> get client async {
    if (!_isInitialized) {
      await _initFuture;
    }
    return _isInitialized ? _client : null;
  }

  // Sync client getter for initialized state
  SupabaseClient? get clientSync => _isInitialized ? _client : null;

  // Check if Supabase is properly configured
  bool get isConfigured => _isInitialized;

  // Authentication methods
  Future<AuthResponse?> signIn(String email, String password) async {
    try {
      final client = await this.client;
      if (client == null) return null;

      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (error) {
      debugPrint('Sign-in failed: $error');
      rethrow;
    }
  }

  Future<AuthResponse?> signUp(String email, String password,
      {Map<String, dynamic>? userData}) async {
    try {
      final client = await this.client;
      if (client == null) return null;

      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: userData,
      );
      return response;
    } catch (error) {
      debugPrint('Sign-up failed: $error');
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      final client = await this.client;
      if (client == null) return;

      await client.auth.signOut();
    } catch (error) {
      debugPrint('Sign-out failed: $error');
      rethrow;
    }
  }

  // Get current user
  User? get currentUser {
    final client = clientSync;
    return client?.auth.currentUser;
  }

  // Listen to auth state changes
  Stream<AuthState> get authStateChanges {
    final client = clientSync;
    return client?.auth.onAuthStateChange ?? const Stream.empty();
  }

  // Generic CRUD operations
  Future<List<dynamic>> select(String table,
      {String columns = '*', String? filter}) async {
    try {
      final client = await this.client;
      if (client == null) return [];

      var query = client.from(table).select(columns);

      if (filter != null) {
        // Simple filter parsing (you can enhance this)
        final filterParts = filter.split('=');
        if (filterParts.length == 2) {
          query = query.eq(filterParts[0].trim(), filterParts[1].trim());
        }
      }

      return await query;
    } catch (error) {
      debugPrint('Select failed: $error');
      return [];
    }
  }

  Future<List<dynamic>?> insert(String table, Map<String, dynamic> data) async {
    try {
      final client = await this.client;
      if (client == null) return null;

      final response = await client.from(table).insert(data).select();
      return response;
    } catch (error) {
      debugPrint('Insert failed: $error');
      rethrow;
    }
  }

  Future<List<dynamic>?> update(String table, Map<String, dynamic> data,
      String column, dynamic value) async {
    try {
      final client = await this.client;
      if (client == null) return null;

      final response =
          await client.from(table).update(data).eq(column, value).select();
      return response;
    } catch (error) {
      debugPrint('Update failed: $error');
      rethrow;
    }
  }

  Future<List<dynamic>?> delete(
      String table, String column, dynamic value) async {
    try {
      final client = await this.client;
      if (client == null) return null;

      final response =
          await client.from(table).delete().eq(column, value).select();
      return response;
    } catch (error) {
      debugPrint('Delete failed: $error');
      rethrow;
    }
  }
}
